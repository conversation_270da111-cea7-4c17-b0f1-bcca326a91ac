[tools]
node = "22.15.0"

[tasks]

# Development Tasks
[tasks.dev]
description = "Start development server"
run = "yarn dev"

# Build Tasks
[tasks.build]
description = "Build the application for production"
run = "yarn build"

[tasks.build-dev]
description = "Build development Docker image"
run = "yarn build-dev"

[tasks.build-prod]
description = "Build production Docker image"
run = "yarn build-prod"

# Docker Compose
[tasks.compose-dev]
description = "Start development environment with Docker Compose"
run = "yarn compose-dev"

# Linting & Formatting
[tasks.lint]
description = "Run linter"
run = "yarn lint"

[tasks.format]
description = "Format code with prettier"
run = "yarn format"

[tasks.format-check]
description = "Check code formatting"
run = "yarn format:check"

# Release Management
[tasks.release]
description = "Create a new release (defaults to patch)"
run = "./scripts/create-release.sh"

[tasks.release-patch]
description = "Create a patch release (0.0.x)"
run = "./scripts/create-release.sh --patch"

[tasks.release-minor]
description = "Create a minor release (0.x.0)"
run = "./scripts/create-release.sh --minor"

[tasks.release-major]
description = "Create a major release (x.0.0)"
run = "./scripts/create-release.sh --major"

[tasks.release-dry-run]
description = "Show what would be released without making changes"
run = "./scripts/create-release.sh --dry-run"

# CI/CD
[tasks.trigger-cd]
description = "Trigger the production CD workflow"
run = "./scripts/run-workflow.sh"

# Cleanup
[tasks.clean]
description = "Clean build artifacts"
run = [
    "rm -rf .next",
    "rm -rf node_modules",
    "rm -rf .turbo"
]

# Dependencies
[tasks.install]
description = "Install dependencies"
run = "yarn install"

[tasks.update-deps]
description = "Update dependencies interactively"
run = "yarn upgrade-interactive --latest"
