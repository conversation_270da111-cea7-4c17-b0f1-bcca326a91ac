# Node modules (they will be installed in the container)
node_modules

# Next.js build output directories
.next
out

# Local environment variables
.env
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-error.log*

# Test coverage directory (if applicable)
coverage

# Version control files and directories
.git
.gitignore

# Optionally, exclude Docker-related files if you don't need them inside the image
Dockerfile
.dockerignore

# Editor and IDE specific files
.vscode
.idea
