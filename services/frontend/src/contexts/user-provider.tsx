"use client";

import { fetcher } from "@/services/fetcher";
import { TUser } from "@/types/auth";
import { createContext, useContext, ReactNode } from "react";
import useSWR, { KeyedMutator, SWRResponse } from "swr";
import { isTokenExpirationError } from "@/utils/auth";
import {
  notifyTokenExpired,
  isTokenExpired,
} from "@/lib/token-expiration-manager";

// Define props for the provider
interface UserProviderProps {
  children: ReactNode;
  initialUser?: TUser | null;
}

// Define the context value interface
interface UserContextType {
  user: TUser | null;
  error: Error | null;
  isLoading: boolean | null;
  mutateUser: KeyedMutator<TUser | null>;
}

// Create the context with a default value of undefined
const UserContext = createContext<UserContextType>({
  user: null,
  error: null,
  isLoading: false,
  mutateUser: async () => null,
});

// UserProvider component
export function UserProvider({ children }: UserProviderProps) {
  // Define the response type for the user API
  type UserResponse = { user: TUser };

  const {
    data: swrUserResponse,
    error: swrError,
    isLoading,
    mutate,
  }: SWRResponse<UserResponse | null, Error> = useSWR<
    UserResponse | null,
    Error
  >("/api/user/global", fetcher, {
    revalidateOnMount: true,
    onErrorRetry: (error: any) => {
      // Handle token expiration
      if (error.status === 401 || isTokenExpirationError(error)) {
        // Only trigger if not already triggered
        if (!isTokenExpired()) {
          notifyTokenExpired();
        }
        return false; // Stop retrying
      }

      // Don't retry on 404
      if (error.status === 404) return;
    },
  });

  // Extract the user from the response
  const swrUser = swrUserResponse?.user || null;

  // Create a wrapper for the mutate function to handle the response structure
  const mutateUser: KeyedMutator<TUser | null> = async (data, options) => {
    // Use type assertion to handle the complex types
    return mutate(
      data ? ({ user: data } as UserResponse) : undefined,
      options as any,
    ).then((res) => (res ? res.user : null));
  };

  return (
    <UserContext.Provider
      value={{
        user: swrUser || null,
        error: swrError || null,
        mutateUser,
        isLoading,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for consuming the context
export function useUser(): UserContextType {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
