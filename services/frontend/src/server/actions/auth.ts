"use server";

import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import {
  ForgotPasswordSchema,
  ForgotPasswordSchemaType,
  ResetPasswordSchema,
  ResetPasswordSchemaType,
  loginSchema,
  loginSchemaType,
  verifyCodeSchema,
  verifyCodeSchemaType,
} from "@/schemas/auth";
import { getUserLanguage } from "@/services/api";
import { CoreAPI } from "@/services/api/core";
import { ActionState, TSystems } from "@/types";
import { cookies } from "next/headers";

const COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "strict",
  path: "/",
} as const;

/**
 * Handles user login and sets refresh token
 */
export const onSubmitLogin = async (
  _prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<loginSchemaType>(
      data,
      loginSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }

    const coreApi = new CoreAPI();
    const { email, password } = validation.data;

    const response = await coreApi.login(email, password);

    (await cookies()).set("main_token", response.main_token, {
      ...COOKIE_CONFIG,
    });

    return {
      success: response.message,
    };
  } catch (err) {
    return handleError(err, "An error occurred during login");
  }
};

// get core session
export const getCoreSession = async (
  prevState: ActionState<null>,
): Promise<ActionState<null>> => {
  try {
    const mainToken = (await cookies()).get("main_token");
    const lang = await getUserLanguage();

    if (!mainToken) {
      return {
        error: "No refresh token found",
        issues: ["Please login again"],
        status: 401,
        redirectTo: `/${lang}/auth/login`,
      };
    }

    const coreApi = new CoreAPI();
    const response = await coreApi.getCoreSession();

    (await cookies()).set("core_session_token", response.session_token, {
      ...COOKIE_CONFIG,
    });

    return {
      success: "Session initialized successfully",
      error: "",
      issues: [],
    };
  } catch (error) {
    await onSubmitLogout();
    return handleError(error, "Failed to initialize session");
  }
};

/**
 * Handles password reset request
 */
export const onSubmitForgotPassword = async (
  prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<ForgotPasswordSchemaType>(
      data,
      ForgotPasswordSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }

    // // Mock email validation
    // if (validation.data.email !== MOCK_USER.email) {
    //   return {
    //     error: "Email not found",
    //     issues: ["No account found with this email"],
    //   };
    // }

    return {
      success: "Password reset instructions sent to your email",
      error: "",
      issues: [],
    };
  } catch (err) {
    return handleError(err, "Failed to send password reset email");
  }
};

/**
 * Handles password reset confirmation
 */
export const onSubmitResetPassword = async (
  prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<ResetPasswordSchemaType>(
      data,
      ResetPasswordSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }

    // Mock successful password reset
    return {
      success: "Password reset successfully",
      error: "",
      issues: [],
    };
  } catch (err) {
    return handleError(err, "Failed to reset password");
  }
};

/**
 * Handles verification code submission
 */
export const onSubmitVerifyCode = async (
  prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<verifyCodeSchemaType>(
      data,
      verifyCodeSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }

    // Mock code verification (accept any 6-digit code)
    const code = validation.data.code;
    if (!/^\d{6}$/.test(code)) {
      return {
        error: "Invalid verification code",
        issues: ["Code must be 6 digits"],
      };
    }

    return {
      success: "Code verified successfully",
      error: "",
      issues: [],
    };
  } catch (err) {
    return handleError(err, "Failed to verify code");
  }
};

/**
 * Handles user logout
 */
export const onSubmitLogout = async (): Promise<
  ActionState<{ redirectTo: string }>
> => {
  try {
    const api = new CoreAPI();
    await api.logout<{ redirectTo: string }>();
    const lang = await getUserLanguage();

    return {
      success: "Logged out successfully",
      redirectTo: `/${lang}/auth/login`,
      error: null,
      issues: null,
    };
  } catch (err) {
    return handleError(err, "Failed to logout");
  }
};

/**
 * Handles session management and prefetches permissions
 */
export async function getSession(
  prevState: ActionState<null>,
  formData: FormData,
): Promise<ActionState<null>> {
  try {
    const system = formData.get("system") as TSystems;
    if (!system) {
      return { error: "Missing system selection" };
    }

    const api = new CoreAPI();

    // Create system session
    const sessionResponse = await api.getSystemSession(system);

    // Store the session token
    (await cookies()).set(
      `${sessionResponse.scope}_session_token`,
      sessionResponse.session_token,
      {
        ...COOKIE_CONFIG,
      },
    );

    // Fetch permissions for the selected system
    try {
      const permissions = await api.getPermissions({ scope: system });
      return {
        success: `${system} session created successfully`,
        error: null,
        issues: null,
        data: permissions.data || [],
      };
    } catch (permissionError) {
      // Return success even if permissions fail, they can be loaded later
      return {
        success: `${system} session created successfully`,
        error: null,
        issues: null,
        data: null,
      };
    }
  } catch (error) {
    return handleError(error, "An unexpected error occurred");
  }
}
