"use client";

import { useEffect, useState, ReactNode } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Clock, AlertTriangle } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  onTokenExpired,
  resetTokenExpiredFlag,
  setLastRouteBeforeExpiry,
} from "@/lib/token-expiration-manager";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { TbLoader } from "react-icons/tb";

export function GlobalAuthDialogProvider({
  children,
}: {
  children: ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const lang: Locale = useLocale() as Locale;
  const router = useRouter();
  const t = useTranslations();
  const pathname = usePathname();
  const isAuthRoute = pathname.startsWith(`/${lang}/auth`);

  useEffect(() => {
    if (isAuthRoute) {
      resetTokenExpiredFlag();
      setOpen(false);
      return () => {};
    }
    const unsubscribe = onTokenExpired(() => {
      setLastRouteBeforeExpiry(pathname);
      setOpen(true);
    });

    return () => unsubscribe();
  }, [isAuthRoute, pathname]);

  if (isAuthRoute) return children;

  const handleConfirm = async () => {
    setIsLoading(true);
    resetTokenExpiredFlag();

    // Check if user has main token to determine redirect destination
    try {
      const response = await fetch("/api/token-status");
      if (response.ok) {
        const data = await response.json();
        const hasMainToken = data.tokens?.main_token?.present;

        if (hasMainToken) {
          router.push(`/${lang}/auth/select-system`);
        } else {
          router.push(`/${lang}/auth/login`);
        }
      } else {
        router.push(`/${lang}/auth/login`);
      }
    } catch (error) {
      router.push(`/${lang}/auth/login`);
    }

    // Close dialog after navigation starts
    setOpen(false);
    setIsLoading(false);
  };

  return (
    <>
      {children}

      <Dialog open={open} onOpenChange={() => {}}>
        <DialogContent
          style={{ borderRadius: "1.25rem" }}
          className="border-0 shadow-2xl bg-white"
          hideClose={true}
          onEscapeKeyDown={(e) => e.preventDefault()}
          onPointerDownOutside={(e) => e.preventDefault()}
          onInteractOutside={(e) => e.preventDefault()}
        >
          {/* Header with Icon */}
          <DialogHeader className="!text-center space-y-4 pb-2">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-amber-50 to-orange-50 rounded-full flex items-center justify-center border border-amber-200">
              <div className="relative">
                <Clock className="w-8 h-8 text-amber-600" />
                <AlertTriangle className="w-4 h-4 text-red-500 absolute -top-1 -left-1" />
              </div>
            </div>

            <div className="space-y-2 text-center">
              <DialogTitle className="text-xl font-semibold text-gray-900 leading-tight">
                {t("auth.sessionExpired.title")}
              </DialogTitle>
              <DialogDescription className="text-base text-gray-600 leading-relaxed px-2">
                {t("auth.sessionExpired.description")}
              </DialogDescription>
            </div>
          </DialogHeader>

          {/* Content Area */}
          <div className="py-4 px-2">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium mb-1">
                    {t("auth.sessionExpired.securityNotice.title")}
                  </p>
                  <p>{t("auth.sessionExpired.securityNotice.description")}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <DialogFooter className="pt-2">
            <Button
              onClick={handleConfirm}
              disabled={isLoading}
              className="w-full h-12 bg-secondary hover:bg-secondary/90 text-white font-medium rounded-[9px] transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <TbLoader size={20} className="text-white animate-spin" />
                </div>
              ) : (
                t("auth.sessionExpired.buttons.continueToLogin")
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
