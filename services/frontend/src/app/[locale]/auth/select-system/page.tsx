"use client";

import { startTransition, useActionState, useEffect } from "react";
import CardWrapper from "@/components/auth/card-wrapper";
import { ActionState, TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { useUser } from "@/contexts/user-provider";
import { getCoreSession } from "@/server/actions/auth";
import SystemSelector from "../../_components/auth/select-system-button";
import ErrorMessage from "@/components/errorMessage";
import { redirect } from "next/navigation";
import { deleteCookie } from "cookies-next/client";

const SelectSystem = () => {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;
  const { mutateUser } = useUser();

  // get global session on mount
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  const [coreState, initCoreSession, isInitializingCore] = useActionState(
    getCoreSession,
    initialState,
  );

  // Initialize the core session immediately when the component mounts
  useEffect(() => {
    const initializeSession = async () => {
      try {
        // Use startTransition to avoid blocking the UI
        startTransition(() => {
          initCoreSession();
        });
      } catch (error) {
        console.error("Session initialization error:", error);
      }
    };

    // Call immediately
    initializeSession();
  }, [initCoreSession]);

  useEffect(() => {
    if (coreState && coreState.success) {
      mutateUser();
    }

    if (coreState && coreState.error) {
      redirect(`/${locale}/auth/login`);
    }
  }, [coreState]);

  useEffect(() => {
    deleteCookie("currSystem");
  }, []);

  return (
    <CardWrapper
      headerTitle={t("auth.selectSystem.headerTitle")}
      headerLabel={t("auth.selectSystem.headerLabel")}
    >
      <SystemSelector locale={locale} t={t} forceLoading={isInitializingCore} />
      {coreState && coreState.error && (
        <ErrorMessage message={coreState.error} />
      )}
    </CardWrapper>
  );
};

export default SelectSystem;
