import CardWrapper from "@/components/auth/card-wrapper";
import React from "react";
import VerifyCodeForm from "../../_components/auth/verify-code-form";
import { getTranslations } from "next-intl/server";

const VerifyCode = async () => {
  const t = await getTranslations("auth");
  return (
    <CardWrapper
      headerTitle={t("verificationCode.title")}
      headerLabel={t("verificationCode.description")}
    >
      <VerifyCodeForm />
    </CardWrapper>
  );
};

export default VerifyCode;
