import { getTranslations } from "next-intl/server";
import { EmployeeLeavesCards } from "../../../../_modules/people/_components/employees/profile";
import { EmployeeLeavesTable } from "../../../../_modules/people/_components/employees/profile/leaves/employee-leaves-table";

export default async function EmployeeLeavesPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const t = await getTranslations();
  const { id: employeeId } = await params;

  return (
    <div className="space-y-6 flex-1 flex flex-col">
      <EmployeeLeavesCards employeeId={employeeId} />
      <EmployeeLeavesTable employeeId={employeeId} />
    </div>
  );
}
