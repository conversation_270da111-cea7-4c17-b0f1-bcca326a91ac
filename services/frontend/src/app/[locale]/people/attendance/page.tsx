import { Suspense } from "react";
import AttendanceEventsTable from "../../_modules/people/_components/attendance-events/table";

const Requests = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <RequestsHeader title="إدارة أحداث الحضور والانصراف للموظفين" />
      <Suspense fallback={<div>Loading...</div>}>
        <AttendanceEventsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Requests;

const RequestsHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="leading-[120%] text-base text-secondary mb-0.5 max-w-full sm:max-w-md md:max-w-lg lg:max-w-xl">
      {title}
    </h2>
  );
};
