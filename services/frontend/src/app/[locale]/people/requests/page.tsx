import React, { Suspense } from "react";
import LeaveRequestsTable from "../../_modules/people/_components/leave-requests/table";
import { getTranslations } from "next-intl/server";

const Requests = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  const t = await getTranslations("people.leaves-requests-page");
  return (
    <>
      <RequestsHeader title={t("table.title")} />
      <Suspense fallback={<div>Loading...</div>}>
        <LeaveRequestsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Requests;

const RequestsHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="leading-[120%] text-base text-secondary mb-0.5 max-w-full sm:max-w-md md:max-w-lg lg:max-w-xl">
      {title}
    </h2>
  );
};
