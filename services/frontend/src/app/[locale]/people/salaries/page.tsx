import React, { Suspense } from "react";
import EmployeesSalariesTable from "../../_modules/people/_components/employees-salaries/table";

const Salaries = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string; sort?: string | string[] }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <SalariesHeader title="إدارة ومعالجة رواتب الموظفين والخصومات" />
      <Suspense fallback={<div>Loading...</div>}>
        <EmployeesSalariesTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Salaries;

const SalariesHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="leading-[120%] text-base text-secondary mb-0.5 max-w-full sm:max-w-md md:max-w-lg lg:max-w-xl">
      {title}
    </h2>
  );
};
