import SystemLayout from "@/components/sidebar/system-layout";
import { SYSTEM } from "@/constants/enum";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("common.layouts");

  return {
    title: "AtharPeople | Athar EMS",
    description: t("people"),
    icons: {
      icon: "/favicons/people-favicon.svg",
    },
  };
}

export default async function PeopleLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <SystemLayout system={`${SYSTEM.PEOPLE}`}>{children}</SystemLayout>;
}
