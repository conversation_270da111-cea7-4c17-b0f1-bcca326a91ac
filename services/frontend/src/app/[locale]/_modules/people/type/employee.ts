import { TSalaryPackageData } from "./salary-package";

// User role relationship in the response
export type TUserRoleRelationship = {
  id: string;
  type: string;
};

// Employee attributes
export type TEmployeeAttributes = {
  name: string;
  email: string;
  user_id: number;
  permissions: Record<string, unknown>[];
  avatar_url: string | null;
  department: string;
  start_date: string;
  status: string | null;
  phone?: string;
  phone_intl?: string;
  department_name: string;
  job_title?: string;
};

// Employee relationships
export type TEmployeeRelationships = {
  user_roles: {
    data: TUserRoleRelationship[];
  };
  salary_package?: {
    data: {
      id: string;
      type: string;
    } | null;
  };
  draft_salary_package?: {
    data: {
      id: string;
      type: string;
    } | null;
  };
  pending_salary_package?: {
    data: {
      id: string;
      type: string;
    } | null;
  };
};

// Employee data structure
export type TEmployeeData = {
  id: string;
  type: string;
  attributes: TEmployeeAttributes;
  relationships: TEmployeeRelationships;
};

// User role attributes
export type TUserRoleAttributes = {
  id: string;
  is_default: boolean | null;
};

// Role and project relationship
export type TRoleProjectRelationship = {
  data: {
    id: string;
    type: string;
  };
};

// User role relationships
export type TUserRoleRelationships = {
  role: TRoleProjectRelationship;
  project: TRoleProjectRelationship;
};

// User role included data
export type TUserRoleIncluded = {
  id: string;
  type: string;
  attributes: TUserRoleAttributes;
  relationships: TUserRoleRelationships;
};

// Legacy type for backward compatibility
export type TUserRole = {
  role: {
    id: string;
    name: string;
    global: boolean;
  };
  project: string | null;
};

// Legacy type for backward compatibility
export type TEmployee = {
  name: string;
  email: string;
  user_id: number;
  id?: string | number;
  start_date: string;
  status: string | null;
  department: string;
  department_name: string;
  job_title?: string;
  phone?: string;
  phone_intl?: string;
  avatar_url?: string | null;
  permissions?: Record<string, unknown>;
  role_name?: string;
  user_roles?: TUserRole[];
};

// Type for included data which can be either user roles or salary package
export type TIncludedData = TUserRoleIncluded | TSalaryPackageData;

// Complete employee response with included data
export type TEmployeeResponse = {
  data: TEmployeeData;
  included?: TIncludedData[];
};

export type TEmployeesResponse = {
  data: TEmployeeData[];
  included?: TIncludedData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type TEmployeeAttachmentAttributes = {
  filename: string;
  content_type: string;
  created_at: string;
  url: string;
  bytes_size: number;
};

export type TEmployeeAttachment = {
  id: string;
  type: string;
  attributes: TEmployeeAttachmentAttributes;
};

export type TEmployeeAttachmentsResponse = {
  data: TEmployeeAttachment[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type EmployeeAttendanceStats = {
  vacationDays: {
    value: number;
    percentageChange: number;
  };
  lateArrival: {
    value: number;
    percentageChange: number;
  };
  lateDeparture: {
    value: number;
    percentageChange: number;
  };
  absence: {
    value: number;
    percentageChange: number;
  };
};

export type EmployeeAttendanceAttributes = EmployeeAttendanceStats;

export type EmployeeLeavesStats = {
  pendingLeaves: {
    value: number;
    percentageChange: number;
  };
  rejectedLeaves: {
    value: number;
    percentageChange: number;
  };
  approvedLeaves: {
    value: number;
    percentageChange: number;
  };
  leaveRequests: {
    value: number;
    percentageChange: number;
  };
};

export type EmployeeLeavesAttributes = EmployeeLeavesStats;

export type EmployeeOvertimeStats = {
  pendingOvertime: {
    value: number;
    percentageChange: number;
  };
  rejectedOvertime: {
    value: number;
    percentageChange: number;
  };
  approvedOvertime: {
    value: number;
    percentageChange: number;
  };
  overtimeRequests: {
    value: number;
    percentageChange: number;
  };
};

export type EmployeeOvertimeAttributes = EmployeeOvertimeStats;
