import { PermissionEnum } from "@/enums/Permission";

/**
 * Utility functions for salary package permissions
 * Handles permission checks for different salary package operations
 */

export interface SalaryPackagePermissions {
  canCreateSalaryPackage: boolean;
  canUpdateSalaryPackage: boolean;
  canCalculateSalary: boolean;
}

/**
 * Get all salary package permissions for a user
 */
export const getSalaryPackagePermissions = (
  hasPermission: (permission: PermissionEnum) => boolean,
  isViewingOwnProfile: boolean,
): SalaryPackagePermissions => {
  // Permission checks based on backend role assignments:
  // - HR roles: create_others (for others only), manage_others, read
  // - Financial Manager: create (general), approve, read
  // - Employees: read_own only
  const canCreateSalaryPackage = isViewingOwnProfile
    ? // For own profile: only general create permission (Financial Manager)
      // create_others is specifically for others, NOT for self
      hasPermission(PermissionEnum.CREATE_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE)
    : // For others' profiles: create_others (HR) OR general create (Financial) OR manage
      hasPermission(PermissionEnum.CREATE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.CREATE_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE);

  const canUpdateSalaryPackage = isViewingOwnProfile
    ? // For own profile: can update own or have general management permissions
      hasPermission(PermissionEnum.UPDATE_OWN_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE)
    : // For others' profiles: need manage_others permission
      hasPermission(PermissionEnum.MANAGE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE);

  const canCalculateSalary = hasPermission(
    PermissionEnum.CALCULATE_SALARY_CALCULATION,
  );

  return {
    canCreateSalaryPackage,
    canUpdateSalaryPackage,
    canCalculateSalary,
  };
};

/**
 * Check if viewing own profile
 */
export const checkIsViewingOwnProfile = (
  currentEmployeeId?: string,
  employeeId?: string,
): boolean => {
  return currentEmployeeId === employeeId;
};
