/**
 * Utility function to get styling classes for salary package status
 * Used across salary package components and salary history modal
 */
export const getSalaryPackageStatusStyles = (status?: string) => {
  switch (status) {
    case "draft":
      return {
        containerClass: "opacity-90 bg-gray-50 border-gray-300",
        textClass: "text-gray-500",
        valueClass: "text-gray-400",
        badgeClass: "bg-gray-100 text-gray-600",
      };
    case "pending_approval":
      return {
        containerClass: "bg-white border-gray-200",
        textClass: "text-gray-500",
        valueClass: "text-amber-600",
        badgeClass: "bg-amber-50 text-amber-600",
      };
    case "approved":
    case "active":
      return {
        containerClass: "bg-white border-gray-200",
        textClass: "text-gray-600",
        valueClass: "text-black",
        badgeClass: "bg-green-50 text-green-600",
      };
    case "rejected":
    case "cancelled":
      return {
        containerClass: "opacity-90 bg-red-50 border-red-200",
        textClass: "text-gray-500",
        valueClass: "text-red-600",
        badgeClass: "bg-red-50 text-red-600",
      };
    default:
      return {
        containerClass: "bg-white border-gray-200",
        textClass: "text-gray-600",
        valueClass: "text-black",
        badgeClass: "bg-gray-50 text-gray-600",
      };
  }
};

export const getSalaryPackageBadgeStyles = (status?: string) => {
  const styles = getSalaryPackageStatusStyles(status);
  return styles.badgeClass;
};
