import { ApprovalRequestData } from "../type/approval-request";

/**
 * Utility functions for handling approval workflow logic
 */

/**
 * Get the current step, whether the user is an approver, and their action (if any)
 */
const getStepAndUserInfo = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
) => {
  if (!approvalRequest || currentUserId <= 0) {
    return { step: null, isApprover: false, userAction: null };
  }

  const { current_step, steps, status } = approvalRequest.attributes;

  if (!current_step || status === "approved" || status === "rejected") {
    return { step: null, isApprover: false, userAction: null };
  }

  const step = steps.find((s) => s.id === current_step.id);
  if (!step) {
    console.warn(`Step with id ${current_step.id} not found in steps array`);
    return { step: null, isApprover: false, userAction: null };
  }

  const userIdString = currentUserId.toString();
  const isApprover = current_step.approver_ids?.includes(userIdString) ?? false;

  const userAction =
    step.actions?.find((a) => a.user_id === userIdString) ?? null;

  return { step, isApprover, userAction };
};

/**
 * Check if the user already acted on the current step
 */
export const hasUserAlreadyActed = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): { hasActed: boolean; action?: "approve" | "reject"; canAct: boolean } => {
  const { step, isApprover, userAction } = getStepAndUserInfo(
    approvalRequest,
    currentUserId,
  );

  return {
    hasActed: !!userAction,
    action: userAction?.action,
    canAct: isApprover && !userAction,
  };
};

/**
 * Check if the user is an approver for the current step
 */
export const isUserApprover = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): boolean => {
  return getStepAndUserInfo(approvalRequest, currentUserId).isApprover;
};

/**
 * Get overall approval status for display
 */
export const getApprovalStatus = (
  approvalRequest: ApprovalRequestData | undefined,
): {
  status: "pending" | "approved" | "rejected" | "completed";
  currentStepName?: string;
  isCompleted: boolean;
} => {
  if (!approvalRequest) {
    return { status: "pending", isCompleted: false };
  }

  const { current_step, status, steps } = approvalRequest.attributes;

  if (!current_step) {
    return {
      status: status === "approved" ? "approved" : "rejected",
      isCompleted: true,
    };
  }

  const step = steps.find((s) => s.id === current_step.id);

  if (step?.rejected) {
    return {
      status: "rejected",
      currentStepName: current_step.name,
      isCompleted: true,
    };
  }

  if (step?.complete) {
    return {
      status: "approved",
      currentStepName: current_step.name,
      isCompleted: false,
    };
  }

  return {
    status: "pending",
    currentStepName: current_step.name,
    isCompleted: false,
  };
};

/**
 * Get user action details for the current step
 */
export const getUserActionInfo = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): {
  hasActed: boolean;
  action?: "approve" | "reject";
  actionDate?: string;
  comment?: string;
} => {
  const { userAction } = getStepAndUserInfo(approvalRequest, currentUserId);

  if (!userAction) return { hasActed: false };

  return {
    hasActed: true,
    action: userAction.action,
    actionDate: userAction.created_at,
    comment: userAction.comment || undefined,
  };
};

/**
 * Check if user has acted on any step in the approval flow
 */
export const hasUserActedOnAnyStep = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): {
  hasActed: boolean;
  action?: "approve" | "reject";
  actionDate?: string;
  comment?: string;
} => {
  if (!approvalRequest || currentUserId <= 0) return { hasActed: false };

  const userIdString = currentUserId.toString();
  const { steps } = approvalRequest.attributes;

  for (const step of steps) {
    const userAction = step.actions?.find((a) => a.user_id === userIdString);
    if (userAction) {
      return {
        hasActed: true,
        action: userAction.action,
        actionDate: userAction.created_at,
        comment: userAction.comment || undefined,
      };
    }
  }

  return { hasActed: false };
};
