import { TEmployee } from "../type/employee";
import { TIncludedEmployee } from "../type/employee-leaves";
import { ApprovalRequestData } from "../type/approval-request";

export function findEmployeeById(
  employeeData: TIncludedEmployee[],
  employeeId: string | undefined,
): TEmployee | null {
  if (!employeeId) return null;

  const foundEmployee = employeeData.find(
    (employee) => employee.id === employeeId,
  )?.attributes;

  return foundEmployee || null;
}

export function extractEmployeesFromIncluded(
  includedData: (TIncludedEmployee | ApprovalRequestData)[] | undefined,
): TIncludedEmployee[] {
  if (!includedData) return [];

  return includedData.filter(
    (item): item is TIncludedEmployee => item.type === "employee",
  );
}

// @returns Employee attributes or null if not found
export function findEmployeeByIdFromIncluded(
  includedData: (TIncludedEmployee | ApprovalRequestData)[] | undefined,
  employeeId: string | undefined,
): TEmployee | null {
  if (!employeeId || !includedData) return null;

  const employees = extractEmployeesFromIncluded(includedData);
  return findEmployeeById(employees, employeeId);
}
