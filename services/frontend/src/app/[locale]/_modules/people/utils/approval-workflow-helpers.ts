import {
  createOptimisticApprovalUpdate,
  OptimisticApprovalAction,
} from "./approval-optimistic-updates";

/**
 * Generic helper to get approval request ID from any entity
 */
export const getApprovalRequestId = (item: any): string | undefined => {
  return item?.relationships?.approval_request?.data?.id;
};

/**
 * Generic helper to create approval workflow updates for any entity
 */
export const createApprovalWorkflowUpdate = <
  T extends { data: any[]; included?: any[] },
>(
  currentData: T | undefined,
  itemId: string,
  action: OptimisticApprovalAction,
  updateItemStatus: (
    item: any,
    isWorkflowComplete: boolean,
    action: OptimisticApprovalAction,
  ) => any,
  comment?: string,
): T | undefined => {
  if (!currentData) return currentData;

  return createOptimisticApprovalUpdate({
    currentData,
    itemId,
    action,
    comment,
    getApprovalRequestId,
    updateItemStatus,
  });
};
