import { TSalaryPackageData } from "../type/salary-package";
import { getIncludedItem } from "./included-data/getIncludedItem";

/**
 * Utility functions for salary package header logic
 * Handles button states, approval status, and package validation
 */

export type SalaryPackageHeaderState = {
  hasPendingSalaryPackage: boolean;
  isUpdateMode: boolean;
  buttonText: string;
  showSubmitButton: boolean | undefined;
  showCancelButton: boolean;
  showHistoryButton: boolean;
};

export const checkHasPendingSalaryPackage = (
  salaryPackage: TSalaryPackageData | null,
  included: any[],
): boolean => {
  if (!salaryPackage) return false;

  // Check both package status and approval request status
  const headerApprovalRequestId =
    salaryPackage?.relationships?.approval_request?.data?.id;
  const headerApprovalRequest = headerApprovalRequestId
    ? getIncludedItem(included, "approval_request", headerApprovalRequestId)
    : null;
  const isApprovalCompleted =
    (headerApprovalRequest?.attributes as any)?.status === "approved";

  return (
    salaryPackage?.attributes.status === "pending_approval" &&
    !isApprovalCompleted
  );
};

/**
 * Get the header state for salary package operations
 */
export const getSalaryPackageHeaderState = (
  salaryPackage: TSalaryPackageData | null,
  editableSalaryPackage: TSalaryPackageData | null,
  included: any[],
): SalaryPackageHeaderState => {
  const hasPendingSalaryPackage = checkHasPendingSalaryPackage(
    salaryPackage,
    included,
  );

  // (editing existing package)
  const isUpdateMode = !!editableSalaryPackage;

  const buttonText = isUpdateMode ? "Update Package" : "Create Package";
  const showSubmitButton =
    !!editableSalaryPackage && editableSalaryPackage.attributes.submittable;
  const showCancelButton = !!editableSalaryPackage;
  // Show history button when there's any salary package
  const showHistoryButton = !!salaryPackage;

  return {
    hasPendingSalaryPackage,
    isUpdateMode,
    buttonText,
    showSubmitButton,
    showCancelButton,
    showHistoryButton,
  };
};

/**
 * Get button click handler based on current state
 */
export const getButtonClickAction = (
  isUpdateMode: boolean,
  editableSalaryPackage: TSalaryPackageData | null,
): "create" | "edit" | "disabled" => {
  if (isUpdateMode && editableSalaryPackage) {
    return "edit";
  } else if (!isUpdateMode) {
    return "create";
  }
  return "disabled";
};
