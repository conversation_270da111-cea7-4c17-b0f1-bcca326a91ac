import { Skeleton } from "@/components/ui/skeleton";

export const MetricCardSkeleton = () => {
  return (
    <div className="bg-white rounded-[20px] p-5 border border-gray-200 flex flex-col gap-4 min-h-[148px]">
      <Skeleton className="h-5 w-32 bg-gray-200" />
      <Skeleton className="h-8 w-16 bg-gray-200" />
      <div className="flex items-center gap-1">
        <Skeleton className="h-4 w-4 bg-gray-200 rounded-full" />
        <Skeleton className="h-4 w-10 bg-gray-200" />
        <Skeleton className="h-4 w-32 bg-gray-200 ml-1" />
      </div>
    </div>
  );
};

export type MetricCardsSkeletonProps = {
  count?: number;
};

export const MetricCardsSkeleton = ({
  count = 4,
}: MetricCardsSkeletonProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <MetricCardSkeleton key={index} />
      ))}
    </div>
  );
};
