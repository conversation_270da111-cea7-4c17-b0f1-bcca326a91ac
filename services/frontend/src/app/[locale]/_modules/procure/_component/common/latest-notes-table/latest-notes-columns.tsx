"use client";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { Locale } from "@/i18n/routing";
import { TFunction } from "@/types";
import { RejectedNote } from "@/types/procure";
import { Button } from "@/components/ui/button";

interface TableMetaWithTranslation extends TableMeta<RejectedNote> {
  t: TFunction;
  locale?: Locale;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<RejectedNote>[] = [
  {
    id: "NoteId",
    accessorKey: "id",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    size: 100,
    header: ({ table }) => {
      return (
        <div className="text-center">
          {getMeta(table).t("procure.HomePage.table.columns.id")}
        </div>
      );
    },
    cell: ({ row }) => {
      return <p className="text-main-2 text-sm  pr-3">#{row.original.id}</p>;
    },
  },
  {
    accessorKey: "name",
    id: "itemName",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("procure.HomePage.table.columns.itemName")}
      </div>
    ),
    size: 100,
    cell: ({ row }) => {
      const name = row.original.itemName;
      return (
        <p className="text-sm font-semibold text-neutral-500 text-center">
          {name}
        </p>
      );
    },
  },

  {
    accessorKey: "rejectedBy",
    id: "rejectedBy",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("procure.HomePage.table.columns.rejectedBy")}
      </div>
    ),
    cell: ({ row }) => {
      const rejectedBy = row.original.rejectedBy;

      return (
        <p className="text-sm font-semibold text-neutral-500 text-center">
          {rejectedBy}
        </p>
      );
    },
  },
  {
    accessorKey: "reason",
    id: "reason",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => (
      <div>{getMeta(table).t("procure.HomePage.table.columns.reason")}</div>
    ),
    cell: ({ row, table }) => {
      const reason = row.original.reason;
      return (
        <div className=" text-wrap max-w-[45.13888889vw] text-sm font-normal text-black">
          {reason}
        </div>
      );
    },
  },

  {
    accessorKey: "actions",
    id: "actions",
    enableHiding: false,
    enableColumnFilter: false,
    size: 140,
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("procure.HomePage.table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const buttonLable = getMeta(table).t("procure.HomePage.table.button");

      return (
        <div className="text-center">
          <Button
            variant={"outline"}
            onClick={(e) => {
              alert(`button Clicked ${row?.original?.id}`);
              e.stopPropagation();
            }}
            className="text-sm font-medium min-w-[111px] text-black hover:bg-gray-50 transition py-4 px-2"
          >
            {buttonLable}
          </Button>
        </div>
      );
    },
  },
];
