"use client";

import { ApprovalDirection } from "@/enums/procure";
import React from "react";
import ApprovalTimeline from "@/components/stepper/ApprovalTimeline";
import { ApprovalItem } from "@/types/procure";

import { CardContent } from "@/components/ui/card";

type StepperWrapperProps = {
  cardTitle: string;
  cardSubtitle?: string;
  direction?: ApprovalDirection;
  approvalItems: ApprovalItem[];
};

// StepperWrapper component for procure process
export default function StepperWrapper({
  direction,
  approvalItems,
  cardTitle,
  cardSubtitle,
}: StepperWrapperProps) {
  return (
    <CardContent className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-medium">{cardTitle}</h2>
        {cardSubtitle && (
          <p className="text-gray-500 text-sm">{cardSubtitle}</p>
        )}
      </div>
      <ApprovalTimeline items={approvalItems} direction={direction} />
    </CardContent>
  );
}
