"use client";

import { ItemsListProps } from "@/types/procure";
import { CardContent } from "@/components/ui/card";
import React from "react";

// Updated props interface for content-only version
type ItemsListContentProps = {
  items: ItemsListProps["items"];
  currencySuffix: string;
  currencySuffixClasses?: string;
  orderSuffix: string;
  rowContainerClassName?: string;
  priceTextClassName?: string;
  orderTextClassName?: string;
  nameTextClassName?: string;
  categoryTextClassName?: string;
  countStyle?: string;
};

export const ItemsList: React.FC<ItemsListContentProps> = ({
  items,
  currencySuffix,
  currencySuffixClasses = "text-sm text-neutral-500 font-semibold",
  orderSuffix,
  rowContainerClassName = "grid grid-cols-3 items-center text-right w-full gap-4",
  priceTextClassName = "text-sm text-black font-semibold text-end",
  orderTextClassName = "text-gray-700 text-center",
  nameTextClassName = "text-gray-800 font-medium flex flex-col items-start",
  categoryTextClassName = "text-gray-400 text-sm font-semibold",
  countStyle = "font-semibold text-sm",
}) => {
  const formatPrice = (value: number) => {
    return (
      <>
        {value} <span className={currencySuffixClasses}>{currencySuffix}</span>
      </>
    );
  };

  const formatOrders = (count: number) => {
    return (
      <>
        <span className={countStyle}>{count}</span>{" "}
        <span className={currencySuffixClasses}>{orderSuffix}</span>
      </>
    );
  };

  return (
    <CardContent className="p-6">
      <div className="flex flex-col gap-6">
        {items.map((item) => (
          <div
            key={item.id}
            className={rowContainerClassName}
            style={{ gridTemplateColumns: "2fr 1fr 1.2fr" }}
          >
            <div className={nameTextClassName} style={{ minWidth: 0 }}>
              <div className="text-base font-semibold">{item.name}</div>
              <div className={categoryTextClassName}>{item.category}</div>
            </div>
            <div className={orderTextClassName} style={{ minWidth: 0 }}>
              {formatOrders(item.orderCount)}
            </div>
            <div className={priceTextClassName} style={{ minWidth: 0 }}>
              {formatPrice(item.price)}
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  );
};
