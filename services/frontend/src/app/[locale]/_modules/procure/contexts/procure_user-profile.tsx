"use client";

import { fetcher } from "@/services/fetcher";
import { TUser } from "@/types/auth";
import { createContext, useContext, ReactNode } from "react";
import useSWR, { KeyedMutator, SWRResponse } from "swr";

// Define props for the provider
type ProcureUserProviderProps = {
  children: ReactNode;
  initialUser?: TUser | null;
};

// Define the context value type
type UserContextType = {
  systemUser: TUser | null;
  error: Error | null;
  isLoading: boolean | null;
  mutate: KeyedMutator<TUser | null>;
};

// Create the context with a default value of undefined
const UserContext = createContext<UserContextType>({
  systemUser: null,
  error: null,
  isLoading: false,
  mutate: async () => null,
});

// procureUserProvider component
export function ProcureUserProvider({ children }: ProcureUserProviderProps) {
  const {
    data: swrUser,
    error: swrError,
    isLoading,
    mutate,
  }: SWRResponse<TUser | null, Error> = useSWR<TUser | null, Error>(
    "/api/user",
    fetcher,
    {
      revalidateOnMount: true,
      onErrorRetry: (error: any) => {
        // Don't retry on 404
        if (error.status === 404) return;
      },
    },
  );

  console.log(swrUser, "from procure system ");

  return (
    <UserContext.Provider
      value={{
        systemUser: swrUser || null,
        error: swrError || null,
        mutate,
        isLoading,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for consuming the context
export function useProcureUser(): UserContextType {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a procureUserProvider");
  }
  return context;
}
