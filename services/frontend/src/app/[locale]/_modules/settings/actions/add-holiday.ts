"use server";

import { ActionState } from "@/types";
import { holidaySchema, HolidaySchemaType } from "../schemas/holiday-schema";
import { THoliday } from "@/types/settings/holidays";
import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import { peopleService } from "@/services/api/people";

export async function addHoliday(
  _prevState: ActionState<THoliday>,
  formData: FormData,
): Promise<ActionState<THoliday>> {
  try {
    // Validate form data using zod schema (no transformation needed!)
    const validation = (await validateFormData(formData, holidaySchema)) as
      | { success: true; data: HolidaySchemaType }
      | ActionState<HolidaySchemaType>;

    if (!isValidationSuccess<HolidaySchemaType>(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
        data: null,
      } as ActionState<THoliday>;
    }

    const res = await peopleService.createHoliday(formData);

    return {
      success: "Holiday added successfully",
      error: "",
      issues: [],
      data: res as THoliday,
    };
  } catch (error) {
    const { error: HolidayError } = handleError(error, "Error adding holiday");

    return {
      error: HolidayError,
      success: "",
      issues: [],
      data: null,
    };
  }
}
