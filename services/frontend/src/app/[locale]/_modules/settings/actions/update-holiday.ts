"use server";

import { ActionState } from "@/types";
import { holidaySchema, HolidaySchemaType } from "../schemas/holiday-schema";
import { THoliday } from "@/types/settings/holidays";
import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import { peopleService } from "@/services/api/people";

export async function updateHoliday(
  holidayId: string,
  _prevState: ActionState<THoliday>,
  formData: FormData,
): Promise<ActionState<THoliday>> {
  try {
    if (!holidayId) {
      return {
        error: "Holiday ID is required",
        success: "",
        issues: ["Please provide a valid holiday ID"],
        data: null,
      };
    }

    // Validate form data using zod schema
    const validation = (await validateFormData(formData, holidaySchema)) as
      | { success: true; data: HolidaySchemaType }
      | ActionState<HolidaySchemaType>;

    if (!isValidationSuccess<HolidaySchemaType>(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
        data: null,
      } as ActionState<THoliday>;
    }

    const res = await peopleService.updateHoliday(holidayId, formData);

    return {
      success: "Holiday updated successfully",
      error: "",
      issues: [],
      data: res as THoliday,
    };
  } catch (err) {
    return handleError(err, "Failed to update holiday", []);
  }
}
