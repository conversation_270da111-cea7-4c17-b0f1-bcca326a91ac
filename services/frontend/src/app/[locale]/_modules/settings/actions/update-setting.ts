"use server";

import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";
import { handleApiError } from "@/utils/api-error-handler";

export async function updateSetting(
  namespace: string,
  key: string,
  value: unknown,
): Promise<ActionState<unknown>> {
  try {
    if (!namespace || !key) {
      return {
        error: "Namespace and key are required",
        success: "",
        issues: ["Please provide valid namespace and key"],
        data: null,
      };
    }

    const settingData = {
      setting: {
        value: value,
      },
    };

    const response = await peopleService.updateSetting(
      namespace,
      key,
      settingData,
    );

    return {
      success: "Setting updated successfully",
      error: "",
      issues: [],
      data: response,
    };
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}
