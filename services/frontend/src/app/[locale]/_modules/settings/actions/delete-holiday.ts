"use server";

import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";
import { handleApiError } from "@/utils/api-error-handler";

export async function deleteHoliday(
  holidayId: string,
): Promise<ActionState<null>> {
  try {
    if (!holidayId) {
      return {
        error: "Holiday ID is required",
        success: "",
        issues: ["Please provide a valid holiday ID"],
        data: null,
      };
    }

    await peopleService.deleteHoliday(holidayId);

    return {
      success: "Holiday deleted successfully",
      error: "",
      issues: [],
      data: null,
    };
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}
