import { z } from "zod";
import { TFunction } from "@/types";
import { isValid, parseISO } from "date-fns";

export const holidaySchema = (t: TFunction) => {
  return z.object({
    name: z
      .string({
        required_error: t("common.form.holiday.error.name-required"),
      })
      .min(1, {
        message: t("common.form.holiday.error.name-required"),
      })
      .max(100, {
        message: t("common.form.holiday.error.name-max"),
      }),
    start_date: z.preprocess(
      (val) => {
        if (typeof val === "string") {
          const parsed = parseISO(val);
          return isValid(parsed) ? parsed : undefined;
        }
        return val;
      },
      z.date({
        required_error: t("common.form.holiday.error.start-date-required"),
      }),
    ),
    end_date: z.preprocess(
      (val) => {
        if (typeof val === "string") {
          const parsed = parseISO(val);
          return isValid(parsed) ? parsed : undefined;
        }
        return val;
      },
      z.date({
        required_error: t("common.form.holiday.error.end-date-required"),
      }),
    ),
  });
};

export type HolidaySchemaType = z.infer<ReturnType<typeof holidaySchema>>;
