import { useState } from "react";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { useToastMessage } from "@/hooks/use-toast-message";
import { deleteHoliday as deleteHolidayAction } from "../actions/delete-holiday";

type UseHolidayMutationsProps = {
  onSuccess?: () => void;
};

export const useHolidayMutations = ({ onSuccess }: UseHolidayMutationsProps = {}) => {
  const t = useTranslations() as TFunction;
  const { showToast } = useToastMessage();
  const [isDeleting, setIsDeleting] = useState(false);

  const deleteHoliday = async (holidayId: string): Promise<boolean> => {
    if (!holidayId) {
      showToast("error", t("common.error.generic"));
      return false;
    }

    setIsDeleting(true);
    try {
      const result = await deleteHolidayAction(holidayId);

      if (result.error) {
        throw new Error(result.error);
      }

      showToast("success", t("settings.holidays.delete-dialog.success-message"));

      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error) {
      showToast("error", t("common.error.generic"));
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    deleteHoliday,
    isDeleting,
  };
};
