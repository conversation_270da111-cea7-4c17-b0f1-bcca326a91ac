"use client";

import { Button } from "@/components/ui/button";
import { useLocale } from "next-intl";
import { useRouter, usePathname } from "next/navigation";

type ActionButtonProps = {
  children: React.ReactNode;
  action: "viewAllRequests" | "viewAllItems";
  className?: string;
};

const ActionButton = ({ children, action, className }: ActionButtonProps) => {
  const router = useRouter();
  const locale = useLocale();
  const pathname = usePathname();

  // Define target paths for each action
  const getTargetPath = (action: string) => {
    switch (action) {
      case "viewAllRequests":
        return `/${locale}/procure/requests`;
      case "viewAllItems":
        return `/${locale}/procure/products`;
      default:
        return "";
    }
  };

  const targetPath = getTargetPath(action);
  const isCurrentPage = pathname === targetPath;

  const handleClick = () => {
    if (isCurrentPage) return;
    router.push(targetPath);
  };

  return (
    <Button
      onClick={handleClick}
      className={className}
      disabled={isCurrentPage}
    >
      {children}
    </Button>
  );
};

export default ActionButton;
