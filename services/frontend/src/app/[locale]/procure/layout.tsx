import SystemLayout from "@/components/sidebar/system-layout";
import { SYSTEM } from "@/constants/enum";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("common.layouts");

  return {
    title: "AtharProcure | Athar EMS",
    description: t("procure"),
    icons: {
      icon: "/favicons/procure-favicon.svg",
    },
  };
}

export default async function ProcureLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <SystemLayout system={`${SYSTEM.PROCURE}`}>{children}</SystemLayout>;
}
