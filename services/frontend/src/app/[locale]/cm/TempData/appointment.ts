import { AppointmentResponse } from "../../_modules/cm/types/appointment-table";

export async function getData(): Promise<AppointmentResponse> {
  // Sample data with 12 appointments for testing purposes.
  const appointments = [
    {
      id: "01",
      number: "01",
      beneficiaryName: "احمد علي",
      time: "29 يناير, 2025",
      description: "استشارة طبية أولية وتقييم الحالة الصحية العامة",
      status: "incoming",
    },
    {
      id: "02",
      number: "02",
      beneficiaryName: "سارة محمد",
      time: "14 مارس, 2015",
      description: "متابعة دورية للعلاج الطبيعي وتقييم التحسن",
      status: "معلقة",
    },
    {
      id: "03",
      number: "03",
      beneficiaryName: "علي حسن",
      time: "30 أكتوبر, 2017",
      description: "جلسة علاج نفسي ومراجعة خطة التأهيل",
      status: "انتظار",
    },
    {
      id: "04",
      number: "04",
      beneficiaryName: "خالد",
      time: "30 أكتوبر, 2017",
      description: "فحص طبي شامل وتحديث الملف الصحي",
      status: "تمت",
    },
    {
      id: "05",
      number: "05",
      beneficiaryName: "ليلى أحمد",
      time: "15 فبراير, 2023",
      description: "جلسة تأهيل حركي وتمارين علاجية متخصصة",
      status: "incoming",
    },
    {
      id: "06",
      number: "06",
      beneficiaryName: "مريم يوسف",
      time: "20 ديسمبر, 2024",
      description: "استشارة اجتماعية وتقييم الاحتياجات الأسرية",
      status: "incoming",
    },
    {
      id: "07",
      number: "07",
      beneficiaryName: "فاطمة علي",
      time: "05 يونيو, 2022",
      description: "متابعة تطور الحالة وتعديل خطة العلاج",
      status: "تمت",
    },
    {
      id: "08",
      number: "08",
      beneficiaryName: "عمرو خالد",
      time: "12 نوفمبر, 2023",
      description: "مراجعة شاملة للحالة وتقييم النتائج",
      status: "انتظار",
    },
    {
      id: "09",
      number: "09",
      beneficiaryName: "ريم إبراهيم",
      time: "25 يوليو, 2023",
      description: "استشارة تخصصية في التأهيل المهني",
      status: "معلقة",
    },
    {
      id: "10",
      number: "10",
      beneficiaryName: "أحمد سمير",
      time: "18 أغسطس, 2022",
      description: "تحديث خطة الرعاية وتقييم التقدم",
      status: "incoming",
    },
    {
      id: "11",
      number: "11",
      beneficiaryName: "نور الهدى",
      time: "01 سبتمبر, 2024",
      description: "جلسة علاج وظيفي وتدريب على المهارات",
      status: "incoming",
    },
    {
      id: "12",
      number: "12",
      beneficiaryName: "كريم سعيد",
      time: "22 أكتوبر, 2022",
      description: "فحص دوري ومتابعة الحالة الصحية",
      status: "تمت",
    },
  ];

  return { data: appointments, totalCount: appointments.length };
}
