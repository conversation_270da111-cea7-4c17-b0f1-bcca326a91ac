"use client";

import { useState } from "react";
import NotificationsList from "@/components/navbar/notification/notification-list";
import { Notification } from "@/types";

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "اقترب موعد جلستك",
      description:
        "تذكير بموعد جلسة العلاج الطبيعي المقررة غداً في تمام الساعة 10:00 صباحاً. يرجى الحضور قبل 15 دقيقة من الموعد المحدد.",
      date: "05:00 | 25-01-2025",
      isRead: false,
    },
    {
      id: 2,
      title: "تم الغاء موعد جلستك",
      description: "تم إلغاء موعد جلسة العلاج النفسي بسبب ظروف طارئة. سيتم التواصل معك لتحديد موعد بديل قريباً.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 3,
      title: "تم استلام التقرير من الطبيب",
      description: "وصل التقرير الطبي الشامل من الطبيب المختص. يمكنك مراجعة التفاصيل والتوصيات في ملفك الشخصي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 4,
      title: "تم استلام التقرير من الطبيب",
      description: "وصل التقرير الطبي الشامل من الطبيب المختص. يمكنك مراجعة التفاصيل والتوصيات في ملفك الشخصي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 5,
      title: "تم استلام التقرير من الطبيب",
      description: "تقرير طبي إضافي تم استلامه مع توصيات جديدة للعلاج. يرجى مراجعة المحتوى والتنسيق مع الفريق الطبي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
  ]);

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true })),
    );
  };

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)),
    );
  };

  return (
    <div className="w-full">
      <NotificationsList
        containerStyle="h-dvh"
        notifications={notifications}
        markAsRead={markAsRead}
        markAllAsRead={markAllAsRead}
      />
    </div>
  );
}
