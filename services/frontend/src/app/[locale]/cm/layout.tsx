import SystemLayout from "@/components/sidebar/system-layout";
import { SYSTEM } from "@/constants/enum";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("common.layouts");

  return {
    title: "AtharCM | Athar EMS",
    description: t("cm"),
    icons: {
      icon: "/favicons/cm-favicon.svg",
    },
  };
}

export default async function CMLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <SystemLayout system={`${SYSTEM.CM}`}>{children}</SystemLayout>;
}
