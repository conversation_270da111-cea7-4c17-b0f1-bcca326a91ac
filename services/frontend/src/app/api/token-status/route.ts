import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { parseJwt } from "@/lib/token-manager";

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();

    const tokens = {
      people_session_token: (await cookieStore).get("people_session_token")
        ?.value,
      core_session_token: (await cookieStore).get("core_session_token")?.value,
      main_token: (await cookieStore).get("main_token")?.value,
      procure_session_token: (await cookieStore).get("procure_session_token")
        ?.value,
    };

    // Store analysis of each token
    const tokenStatus: Record<
      string,
      {
        present: boolean;
        expired?: boolean | null;
        expiresIn?: number | null;
        error?: string;
      }
    > = {};

    for (const [name, value] of Object.entries(tokens)) {
      if (!value) {
        tokenStatus[name] = { present: false };
        continue;
      }

      try {
        const decoded = parseJwt(value);
        const expirationTime = decoded?.exp ? decoded.exp * 1000 : null;

        tokenStatus[name] = {
          present: true,
          expired: expirationTime ? Date.now() > expirationTime : null,
          expiresIn: expirationTime
            ? Math.floor((expirationTime - Date.now()) / 1000)
            : null,
        };
      } catch {
        tokenStatus[name] = {
          present: true,
          error: "Failed to parse token",
        };
      }
    }

    return NextResponse.json({ tokens: tokenStatus });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
