import { NextRequest, NextResponse } from "next/server";
import { CoreAPI } from "@/services/api/core";
import { TSystems } from "@/types";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const scope = (searchParams.get("scope") as TSystems) || "core";

    console.log("🔍 PERMISSIONS API ROUTE:");
    const coreApi = new CoreAPI();
    const response = await coreApi.getPermissions({ scope });

    // TEMPORARY: Filter out all attendance_event permissions
    // if (response.data && Array.isArray(response.data)) {
    //   response.data = response.data.filter(
    //     (permission: any) => !permission.id.includes("attendance_event"),
    //   );
    //   console.log(
    //     "🚫 TEMPORARILY FILTERED OUT all attendance_event permissions",
    //   );
    // }

    return NextResponse.json(response);
  } catch (error) {
    console.error("🚨 Error fetching system user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch system user data" },
      { status: 500 },
    );
  }
}
