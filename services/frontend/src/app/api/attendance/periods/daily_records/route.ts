import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeId = searchParams.get("employee_id");
    const date = searchParams.get("date");

    if (!employeeId) {
      return NextResponse.json(
        { error: "Missing employee_id parameter" },
        { status: 400 },
      );
    }

    if (!date) {
      return NextResponse.json(
        { error: "Missing date parameter" },
        { status: 400 },
      );
    }

    try {
      const response = await peopleService.getDaySummary(employeeId, date);
      if (!response) {
        return NextResponse.json(
          { error: "No attendance data found" },
          { status: 404 },
        );
      }
      return NextResponse.json(response);
    } catch (error) {
      // Re-throw for the outer catch block to handle
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET attendance daily records route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
