import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(_request: NextRequest) {
  try {
    try {
      const response = await peopleService.getAttendanceExemptions();

      return NextResponse.json(response);
    } catch (error) {
      return NextResponse.json(
        { error: "Failed to fetch Attendance list" },
        { status: 500 },
      );
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Attendacne Exemptions route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
