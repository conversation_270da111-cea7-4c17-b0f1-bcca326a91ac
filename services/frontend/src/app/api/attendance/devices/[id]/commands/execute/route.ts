import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Device ID is required" },
        { status: 400 },
      );
    }

    // Parse the URL search params (query parameters)
    const url = new URL(request.url);
    const queryParams = url.searchParams;

    const response = await peopleService.executeDeviceCommand(id, queryParams);

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "POST Device Command Execute route",
    );

    return NextResponse.json({ error: errorMessage }, { status });
  }
}
