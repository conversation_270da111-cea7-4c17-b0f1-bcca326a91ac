import { NextRequest, NextResponse } from "next/server";
import { coreAPI } from "@/services/api/core";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const response = await coreAPI.getProject(id);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching project:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to fetch project", message: errorMessage },
      { status: 500 },
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const response = await coreAPI.updateProject(id, body);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating project:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to update project", message: errorMessage },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    await coreAPI.deleteProject(id);
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting project:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to delete project", message: errorMessage },
      { status: 500 },
    );
  }
}
