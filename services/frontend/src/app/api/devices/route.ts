import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get("search") || "";

    const filterParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        filterParams.append(key, value);
      }
    }

    const filters = filterParams.toString();

    const page = parseInt(searchParams.get("page") || "5", 5);
    const limit = parseInt(searchParams.get("limit") || "5", 5);
    const sort = searchParams.get("sort") || "created_at";

    const response = await peopleService.getDevices(
      page,
      limit,
      sort,
      search,
      filters,
    );

    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Attendacne Exemptions route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
