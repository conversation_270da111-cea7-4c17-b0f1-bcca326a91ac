import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract query parameters with defaults
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const search = searchParams.get("search") || "";
    const include = searchParams.get("include") || "";
    const sort = searchParams.get("sort") || "-updated_at";

    // Build filters string from search params
    // This will collect all filter parameters and format them for the API
    let filters = "";
    searchParams.forEach((value, key) => {
      if (key.startsWith("filter")) {
        filters += `${key}=${encodeURIComponent(value)}&`;
      }
    });

    // Remove trailing & if it exists
    if (filters.endsWith("&")) {
      filters = filters.slice(0, -1);
    }

    // Call the service method to get the data
    const response = await peopleService.getGlobalLeavesData(
      page,
      limit,
      include,
      search,
      sort,
      filters,
    );

    // Return the response
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching global leaves data:", error);
    return NextResponse.json(
      { error: "Failed to fetch leaves data" },
      { status: 500 },
    );
  }
}
