import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    try {
      const { searchParams } = new URL(request.url);
      const include = searchParams.get("include") || "";

      const response = await peopleService.getEmployeeById(id, include);
      return NextResponse.json(response);
    } catch (error) {
      // Check if it's a 404 error
      if (
        error instanceof Error &&
        (error as { status?: number }).status === 404
      ) {
        return NextResponse.json(
          { error: "Employee not found" },
          { status: 404 },
        );
      }

      // Re-throw for the outer catch block to handle
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET employee route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
