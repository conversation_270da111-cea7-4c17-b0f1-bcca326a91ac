import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    const response = await peopleService.getEmployeeAttachments(id);
    if (!response || !response.data) {
      return NextResponse.json({
        data: [],
        meta: {
          pagination: {
            count: 0,
            page: page,
            limit: limit,
            from: 0,
            to: 0,
          },
        },
      });
    }

    const responseData = response;

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error in GET employee attachments route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
