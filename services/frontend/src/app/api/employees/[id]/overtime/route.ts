import { NextRequest, NextResponse } from "next/server";
import { EmployeeOvertimeStats } from "@/app/[locale]/_modules/people/type/employee";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    // For now, we'll return mock data based on the image
    const overtimeStats: EmployeeOvertimeStats = {
      pendingOvertime: {
        value: 1,
        percentageChange: 12,
      },
      rejectedOvertime: {
        value: 3,
        percentageChange: 12,
      },
      approvedOvertime: {
        value: 7,
        percentageChange: 12,
      },
      overtimeRequests: {
        value: 11,
        percentageChange: 12,
      },
    };

    return NextResponse.json({
      data: {
        id: id,
        type: "employee_overtime",
        attributes: overtimeStats,
      },
      meta: {
        // Any additional metadata can go here
      },
    });
  } catch (error) {
    console.error("Error in GET employee overtime route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
