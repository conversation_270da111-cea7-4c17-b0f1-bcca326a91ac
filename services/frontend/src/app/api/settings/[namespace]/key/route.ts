import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

type RouteParams = {
  params: Promise<{
    namespace: string;
    key: string;
  }>;
};

export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    const { namespace, key } = await params;
    const response = await peopleService.getSetting(namespace, key);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET setting route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
