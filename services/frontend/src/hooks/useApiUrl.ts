import { useMemo } from 'react';
import qs from 'qs';
import { formatFilterValues } from '@/components/table/filter/ransack-utils';

interface UseApiUrlParams {
  baseUrl: string;
  page: number;
  limit: number;
  sort: string;
  search?: string;
  filters: Record<string, any>;
  tableId: string;
}

export function useApiUrl({
  baseUrl,
  page,
  limit,
  sort,
  search,
  filters,
  tableId
}: UseApiUrlParams): string {
  return useMemo(() => {
    // Use universal helper - handles ALL type conversion
    const formattedFilters = tableId && Object.keys(filters).length > 0
      ? formatFilterValues(filters, tableId)
      : filters;

    // Build params object for qs
    const params = {
      page,
      limit,
      sort,
      ...(search && { search }),
      ...(Object.keys(formattedFilters).length > 0 && { filter: formattedFilters })
    };

    // qs converts arrays to duplicate parameter names
    const queryString = qs.stringify(params, {
      arrayFormat: 'repeat',  // filter[name_cont]=John&filter[name_cont]=Smith
      encode: false
    });

    return `${baseUrl}?${queryString}`;
  }, [baseUrl, page, limit, sort, search, filters, tableId]);
}
