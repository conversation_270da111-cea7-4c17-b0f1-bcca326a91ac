import { TinputField, TFunction } from "@/types";
import {
  ForgotPasswordSchemaType,
  ResetPasswordSchemaType,
  loginSchemaType,
  verifyCodeSchemaType,
} from "@/schemas/auth";

export const getLoginForm = (t: TFunction): Tin<PERSON><PERSON>ield<loginSchemaType>[] => [
  {
    name: "email",
    type: "email",
    label: t("common.form.email.label"),
    placeholder: t("common.form.email.placeholder"),
  },
  {
    name: "password",
    type: "password",
    label: t("common.form.password.label"),
    placeholder: "•••••••••",
  },
];

export const getForgotPasswordForm = (
  t: TFunction,
): TinputField<ForgotPasswordSchemaType>[] => [
  {
    name: "email",
    type: "email",
    label: t("common.form.email.label"),
    placeholder: t("common.form.email.placeholder"),
  },
];

export const getResetPasswordForm = (
  t: TFunction,
): TinputField<ResetPasswordSchemaType>[] => [
  {
    name: "password",
    type: "password",
    label: t("common.form.password.label"),
    placeholder: "•••••••••",
  },
  {
    name: "confirmPassword",
    type: "password",
    label: t("common.form.confirmPassword.label"),
    placeholder: "•••••••••",
  },
];

export const getVerifyCodeForm = (
  t: TFunction,
): TinputField<verifyCodeSchemaType>[] => [
  {
    name: "code",
    type: "number",
    label: t("common.form.verifyCode.label"),
    placeholder: t("common.form.verifyCode.placeholder"),
  },
];
