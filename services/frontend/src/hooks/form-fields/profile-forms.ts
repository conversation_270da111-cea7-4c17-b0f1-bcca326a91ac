import { TinputField, TFunction } from "@/types";
import {
  changePasswordSchemaType,
  editProfileSchemaType,
} from "@/schemas/settings";

export const getChangePasswordForm = (
  t: TFunction,
): TinputField<changePasswordSchemaType>[] => [
  {
    name: "currentPassword",
    type: "password",
    label: t("common.form.password.currLabel"),
    placeholder: "•••••••••",
    disableToggle: true,
  },
  {
    name: "password",
    type: "password",
    label: t("common.form.password.newLabel"),
    placeholder: "•••••••••",
  },
  {
    name: "confirmPassword",
    type: "password",
    label: t("common.form.confirmPassword.newLabel"),
    placeholder: "•••••••••",
  },
];

export const getEditProfileForm = (
  t: TFunction,
): TinputField<editProfileSchemaType>[] => [
  {
    name: "avatar",
    type: "file",
    label: t("common.form.profile-image.label"),
    avatarSize: "md",
    containerClassName: "mb-6",
    deleteTranslationKey: "common.form.profile-image.buttons.delete",
    changeTranslationKey: "settings.global.buttons.profile.change-image",
    buttonsClassName: "mb-10 md:mb-0",
  },
  {
    name: "name",
    type: "text",
    label: t("common.form.name.label"),
    placeholder: t("common.form.name.update.placeholder"),
    rightIcon: "Profile",
  },
  {
    name: "email",
    type: "email",
    label: t("common.form.email.label"),
    placeholder: "update your email address",
    labelClassName: "text-neutral-500",
    readOnly: true,
  },
  {
    name: "password",
    type: "password",
    label: t("common.form.password.label"),
    placeholder: "•••••••••",
    labelClassName: "text-neutral-500",
    readOnly: true,
  },
];
