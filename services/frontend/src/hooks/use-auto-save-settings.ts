import { useCallback } from "react";
import { updateSetting } from "@/app/[locale]/_modules/settings/actions/update-setting";

export const useAutoSaveSettings = () => {
  const saveSetting = useCallback(
    async (namespace: string, key: string, value: unknown) => {
      const result = await updateSetting(namespace, key, value);

      if (result.error) {
        throw new Error(result.error);
      }

      return result.data;
    },
    [],
  );

  return {
    saveSetting,
  };
};
