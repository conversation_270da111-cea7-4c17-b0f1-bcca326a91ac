import { useState, useEffect } from "react";

function useMediaQuery(query: string, defaultMatches = false): boolean {
  // For mobile queries, it's better to default to true for mobile-first approach
  // This ensures the mobile UI is shown during SSR and initial render
  const [matches, setMatches] = useState(defaultMatches);

  // This effect runs only once after hydration
  useEffect(() => {
    // This is for SSR - if window is not available, return early
    if (typeof window !== "object") return;

    // Set up the media query
    const mediaQuery = window.matchMedia(query);

    // Function to update matches state
    const updateMatches = () => setMatches(mediaQuery.matches);

    // Set initial value after hydration
    updateMatches();

    // Add listener for changes
    mediaQuery.addEventListener("change", updateMatches);

    // Cleanup
    return () => mediaQuery.removeEventListener("change", updateMatches);
  }, [query]); // Only re-run if query changes

  return matches;
}

export default useMediaQuery;
