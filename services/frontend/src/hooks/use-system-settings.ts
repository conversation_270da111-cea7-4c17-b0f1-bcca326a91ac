import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { usePermission } from "@/contexts/PermissionContext";
import { SystemSettingsResponse } from "@/types/settings/system-settings";

export const useSystemSettings = (namespace: string) => {
  const apiUrl = `/api/settings?sort=namespace,key&filter[namespace_eq]=${namespace}`;
  const { hasPermission } = usePermission();
  const canRead = hasPermission("read:setting");

  const { data, error, isLoading, mutate } = useSWR<SystemSettingsResponse>(
    canRead ? apiUrl : null,
    fetcher,
  );
  console.log(data);
  return {
    settings: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Specialized hooks for each settings tab
export const useCompanySettings = () => useSystemSettings("company");
export const useAttendanceSettings = () => useSystemSettings("attendance");
export const useSalarySettings = () => useSystemSettings("payroll");
