import { useEffect } from "react";

type UseClickOutsideProps = {
  ref: React.RefObject<HTMLElement | HTMLDivElement | null>;
  handler: () => void;
  excludeSelectors?: string[];
};

export default function useClickOutside({
  ref,
  handler,
  excludeSelectors = [],
}: UseClickOutsideProps) {
  useEffect(() => {
    const listener = (event: MouseEvent) => {
      if (!ref.current || ref.current.contains(event.target as Node)) return;

      const target = event.target as Element;
      const isExcluded = excludeSelectors.some((selector) =>
        target.closest(selector),
      );
      if (!isExcluded) handler();
    };

    document.addEventListener("mousedown", listener);
    return () => document.removeEventListener("mousedown", listener);
  }, [ref, handler, excludeSelectors]);
}
