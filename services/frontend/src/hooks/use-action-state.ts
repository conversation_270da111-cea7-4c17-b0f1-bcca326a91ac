"use client";

import { useState, useTransition } from "react";
import { ActionState } from "@/types";

export function useActionState<T>(
  action: (prev: ActionState<T>, fd: FormData) => Promise<ActionState<T>>,
  initial: ActionState<T>
): [ActionState<T>, (fd: FormData) => void, boolean] {
  const [state, setState] = useState<ActionState<T>>(initial);
  const [isPending, startTransition] = useTransition();

  const submitAction = (formData: FormData) => {
    startTransition(() => {
      action(state, formData).then((newState) => setState(newState));
    });
  };

  return [state, submitAction, isPending];
}
