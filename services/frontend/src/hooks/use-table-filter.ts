import { useState, useRef } from "react";
import { TFilterGroup, FilterRule } from "@/components/table/types";

export const useTableFilter = () => {
  // Create a default group with timestamp-based IDs
  const createDefaultGroup = (): TFilterGroup[] => {
    const timestamp = Date.now();
    return [
      {
        id: `group-${timestamp}`,
        rules: [
          {
            id: `rule-${timestamp + 1}`, // Add 1 to ensure uniqueness
            field: "",
            operator: "",
            value: "",
          },
        ],
      },
    ];
  };

  const defaultGroup = createDefaultGroup();

  const [groups, setGroups] = useState<TFilterGroup[]>(defaultGroup);
  const [lastAddedRuleId, setLastAddedRuleId] = useState<string | null>(null);
  const ruleRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const addRule = (groupId: string): string => {
    const newRuleId = `rule-${Date.now()}`;
    setGroups((prevGroups) =>
      prevGroups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            rules: [
              ...group.rules,
              {
                id: newRuleId,
                field: "",
                operator: "",
                value: "",
              },
            ],
          };
        }
        return group;
      }),
    );
    setLastAddedRuleId(newRuleId);
    return newRuleId;
  };

  const removeRule = (groupId: string, ruleId: string) => {
    setGroups((prevGroups) =>
      prevGroups.map((group) => {
        if (group.id === groupId) {
          // If this would be the last rule, don't remove it
          if (group.rules.length === 1) {
            return {
              ...group,
              rules: [
                {
                  id: `rule-${Date.now()}`,
                  field: "",
                  operator: "",
                  value: "",
                },
              ],
            };
          }
          return {
            ...group,
            rules: group.rules.filter((rule) => rule.id !== ruleId),
          };
        }
        return group;
      }),
    );
  };

  const handleFilterChange = (
    groupId: string,
    ruleId: string,
    field: keyof FilterRule,
    value: any, // Support all types: string, Date, boolean, number, etc.
  ) => {
    setGroups((prevGroups) => {
      return prevGroups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            rules: group.rules.map((rule) => {
              if (rule.id === ruleId) {
                if (field === "field") {
                  return {
                    ...rule,
                    field: value as string,
                    operator: "",
                    value: "",
                  };
                }
                return {
                  ...rule,
                  [field]: value,
                };
              }
              return rule;
            }),
          };
        }
        return group;
      });
    });
  };

  const clearFilters = () => {
    // Use the createDefaultGroup function for consistency
    setGroups(createDefaultGroup());
  };

  return {
    groups,
    setGroups,
    ruleRefs,
    lastAddedRuleId,
    setLastAddedRuleId,
    addRule,
    removeRule,
    handleFilterChange,
    clearFilters,
  };
};
