import { MiddlewareChain } from "./middleware/middleware-factory";
import { authMiddleware } from "./middleware/auth";
import { localizationMiddleware } from "./middleware/localization";
import { NextRequest, NextResponse } from "next/server";

const middlewareChain = new MiddlewareChain()
  .add(authMiddleware)
  .add(localizationMiddleware);

export async function middleware(request: NextRequest) {
  try {
    return await middlewareChain.execute(request);
  } catch (error) {
    console.error("Middleware error:", error);
    return NextResponse.redirect(new URL("/error", request.nextUrl.origin));
  }
}

export const config = {
  matcher: ["/", "/(ar|en)/:path*"],
};
