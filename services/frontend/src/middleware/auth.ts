import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const mainToken = request.cookies.get("main_token")?.value;
  const scopedToken = request.cookies.get("core_session_token")?.value;

  // Extract language from the URL's first segment, falling back to cookie or default "en"
  const segments = pathname.split("/");
  const langFromPath = segments[1];
  const langFromCookie = request.cookies.get("NEXT_LOCALE")?.value;
  const lang = ["en", "ar"].includes(langFromPath)
    ? langFromPath
    : langFromCookie || "ar";

  // Define public paths that don't require authentication
  const publicPaths = new Set([
    `/${lang}/auth/verify-code`,
    `/${lang}/auth/forgot-password`,
    `/${lang}/auth/reset-password`,
    `/${lang}/auth/login`,
    `/${lang}/select-system`,
  ]);

  // If user is already authenticated, they should not access the login page.
  if (
    mainToken &&
    (pathname === "/auth/login" || pathname === `/${lang}/auth/login`)
  ) {
    return NextResponse.redirect(
      new URL(`/${lang}/auth/select-system`, request.nextUrl.origin),
    );
  }

  // Allow public paths without further checks
  if (publicPaths.has(pathname)) {
    return NextResponse.next();
  }

  // If no refresh token, redirect to login
  // If session expired or logout occurred (no tokens), redirect to login
  if (!mainToken && !pathname.includes("/auth/login")) {
    return NextResponse.redirect(
      new URL(`/${lang}/auth/login`, request.nextUrl.origin),
    );
  }
  // If refresh token exists but scoped token is missing, redirect to select-system unless already there
  if (!scopedToken && !pathname.endsWith("/auth/select-system")) {
    return NextResponse.redirect(
      new URL(`/${lang}/auth/select-system`, request.nextUrl.origin),
    );
  }

  // If user lands on the base language route (e.g. '/en' or '/ar') and has valid tokens, redirect to select-system
  if (
    ["en", "ar"].includes(lang) &&
    pathname === `/${lang}` &&
    mainToken &&
    scopedToken
  ) {
    return NextResponse.redirect(
      new URL(`/${lang}/auth/select-system`, request.nextUrl.origin),
    );
  }

  return null;
}
