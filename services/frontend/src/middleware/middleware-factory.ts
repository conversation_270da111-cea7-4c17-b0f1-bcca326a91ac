import { NextRequest, NextResponse } from "next/server";

export type MiddlewareFunction = (
  request: NextRequest,
) => Promise<NextResponse | null>;

export class MiddlewareChain {
  private middlewares: MiddlewareFunction[] = [];

  add(middleware: MiddlewareFunction) {
    this.middlewares.push(middleware);
    return this;
  }

  async execute(request: NextRequest): Promise<NextResponse> {
    for (const middleware of this.middlewares) {
      try {
        const response = await middleware(request);
        if (response) return response;
      } catch (error) {
        console.error(`Middleware error:`, error);
        throw error;
      }
    }
    return NextResponse.next();
  }
}
