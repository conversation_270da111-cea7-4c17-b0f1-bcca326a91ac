export interface Column<T> {
  /** Column header label */
  header: string;
  /** Key of the field in each row object */
  accessor: keyof T;

  /** Tailwind classes for each cell in this column (unless overridden by linkClassName when isLink=true) */
  cellClassName?: string;
  width?: string; // e.g. "auto", "1fr", "2fr"

  /** If true, render this cell as an <a> tag */
  isLink?: boolean;
  /** Prefix to display before the cell’s value (e.g. "#") */
  linkPrefix?: string;
  /** Prefix to prepend to the href (e.g. "/orders/") */
  linkHrefPrefix?: string;
  /** Tailwind classes for the <a> tag itself */
  linkClassName?: string;

  /** If true, render an action button instead of text/link */
  isAction?: boolean;
  /** Label for the action button */
  actionLabel?: string;
  /** Click handler for the action button (receives entire row data) */
  onActionClick?: (row: T) => void;
}

export interface DynamicTableProps<T> {
  columns: Column<T>[];
  data: T[];
  headerText?: string;
  headerTitleClassName?: string;
  /** Outer container (div) classes */
  tableClassName?: string;
  /** <tr> classes for the thead */
  headerRowClassName?: string;
  /** <th> classes */
  headerCellClassName?: string;
  /** <tr> classes for each tbody row */
  bodyRowClassName?: string;
  /** <td> default classes (used when col.cellClassName and col.linkClassName are not set) */
  bodyCellClassName?: string;
}
