import { ApprovalStatus } from "@/enums/procure";

export type ApprovalItem = {
  id: string;
  title: string;
  subtitle: string;
  status: ApprovalStatus;
  date: string;
};

export type Item = {
  id: string | number;
  name: string;
  category: string;
  orderCount: number;
  price: number;
};

export type ItemsListProps = {
  items: Item[];
  title: string;
  actionText: string;
  onActionClick: () => void;
  currencySuffix: string;
  currencySuffixClasses?: string;
  orderSuffix: string;
  className?: string;
  headerTitleClassName?: string;
  headerButtonClassName?: string;
  rowContainerClassName?: string;
  priceTextClassName?: string;
  orderTextClassName?: string;
  nameTextClassName?: string;
  categoryTextClassName?: string;
};

export type RejectedNote = {
  id: string | number;
  orderNumber: string;
  itemName: string;
  rejectedBy: string;
  reason: string;
} & Record<string, unknown>;
