export interface ExtractedError {
  message: string;
  statusCode: number;
}

export function extractApiError(error: unknown): ExtractedError {
  let errorMessage = "Internal Server Error";
  let statusCode = 500;

  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === "string") {
    errorMessage = error;
  } else if (error && typeof error === "object") {
    const errorObj = error as any;

    // Try to extract error message from various structures
    if (errorObj.message) {
      errorMessage = errorObj.message;
    } else if (errorObj.error) {
      errorMessage = errorObj.error;
    } else if (errorObj.response?.data?.message) {
      errorMessage = errorObj.response.data.message;
    } else if (errorObj.response?.data?.error) {
      errorMessage = errorObj.response.data.error;
    }

    // Try to extract status code
    if (errorObj.status) {
      statusCode = errorObj.status;
    } else if (errorObj.response?.status) {
      statusCode = errorObj.response.status;
    }
  }

  return {
    message: errorMessage,
    statusCode,
  };
}

/**
 * Helper function to create a standardized error response for API routes
 */
export function createErrorResponse(error: unknown, context?: string) {
  const { message, statusCode } = extractApiError(error);

  // Log the error for debugging
  if (context) {
    console.error(`Error in ${context}:`, error);
  } else {
    console.error("API Error:", error);
  }

  return {
    error: message,
    status: statusCode,
  };
}
