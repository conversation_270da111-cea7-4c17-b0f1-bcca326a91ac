"use client";

import { KeyedMutator } from "swr";
import { TUser } from "@/types/auth";

export const performLogout = async (
  clearPermissions: () => void,
  mutateUser: KeyedMutator<TUser | null>,
  logoutAction: () => void,
  additionalCleanup?: () => void,
) => {
  try {
    clearPermissions();
    mutateUser(null);
    if (additionalCleanup) {
      additionalCleanup();
    }
    // 4. Execute server-side logout action
    logoutAction();
  } catch (error) {
    logoutAction();
  }
};

//  Clears permissions and other session data from browser storage
export const clearClientSession = () => {
  if (typeof window !== "undefined") {
    sessionStorage.removeItem("user_permissions");
  }
};
