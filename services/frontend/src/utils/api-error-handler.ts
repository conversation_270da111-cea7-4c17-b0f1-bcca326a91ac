export function isEmptyResponseError(error: unknown): boolean {
  return (
    error instanceof Error &&
    (error.message.includes("end of JSON") ||
      error.message.includes("Unexpected end of JSON input") ||
      error.message.includes("unexpected end of input"))
  );
}
export function isNotFoundError(error: unknown): boolean {
  return (
    error instanceof Error && (error as { status?: number })?.status === 404
  );
}

export function handleApiError<T>(
  error: unknown,
  defaultResponse: T = { success: true } as unknown as T,
): T | null {
  // Handle empty response errors
  if (isEmptyResponseError(error)) {
    return defaultResponse;
  }

  // Handle 404 errors
  if (isNotFoundError(error)) {
    return {
      ...defaultResponse,
      message: "Resource not found or already deleted",
    } as unknown as T;
  }

  // For other errors, return null to indicate the error should be rethrown
  return null;
}
