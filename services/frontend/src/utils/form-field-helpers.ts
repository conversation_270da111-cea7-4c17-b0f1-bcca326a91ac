import { TinputField, TFunction } from "@/types";
import { FormInputType } from "@/types/form-types";

export function createBooleanSwitchField<T extends Record<string, unknown>>(
  name: keyof T,
  label: string,
  options: Partial<TinputField<T>> = {},
): Tinput<PERSON>ield<T> {
  return {
    name,
    type: "switch",
    label,
    ...options,
  };
}

export function createBooleanSelectField<T extends Record<string, unknown>>(
  name: keyof T,
  label: string,
  t: TFunction,
  options: Partial<TinputField<T>> = {},
): TinputField<T> {
  return {
    name,
    type: "select",
    label,
    placeholder: t("common.form.select.placeholder") || "Select an option",
    options: [
      { label: t("common.form.select.yes") || "Yes", value: "true" },
      { label: t("common.form.select.no") || "No", value: "false" },
    ],
    ...options,
  };
}

export function convertBooleanValue(
  value: boolean | string,
  fieldType: "switch" | "select",
): boolean | string {
  if (fieldType === "switch") {
    return value === true || value === "true";
  }
  // For select fields, convert to string
  return String(value === true || value === "true");
}

export function convertToBooleanForAPI(value: unknown): boolean {
  return value === true || value === "true";
}

export function getInputTypeFromAPI(apiType: string): FormInputType {
  switch (apiType.toLowerCase()) {
    case "boolean":
      return "switch";
    case "integer":
    case "float":
    case "number":
    case "decimal":
      return "number";
    case "email":
      return "email";
    case "phone":
    case "mobile":
      return "tel";
    case "time":
      return "time";
    case "date":
      return "date";
    case "datetime":
    case "datetime-local":
      return "datetime-local";
    case "url":
    case "website":
      return "url";
    case "password":
      return "password";
    case "textarea":
    case "longtext":
      return "textarea";
    case "file":
    case "image":
      return "file";
    case "select":
    case "dropdown":
      return "select";
    case "array":
      return "multi-select"; // Arrays will be handled as multi-select
    case "string":
    case "text":
    default:
      return "text";
  }
}

export function convertValueForAPISubmission(
  value: unknown,
  apiType: string,
): unknown {
  switch (apiType.toLowerCase()) {
    case "boolean":
      return value === true || value === "true";
    case "integer":
      return parseInt(String(value), 10);
    case "float":
    case "number":
    case "decimal":
      return parseFloat(String(value));
    case "array":
      if (Array.isArray(value)) {
        return value.map((item) => String(item).padStart(2, "0"));
      }
      return [];
    case "string":
    case "text":
    case "email":
    case "phone":
    case "mobile":
    case "time":
    case "date":
    case "url":
    case "password":
    case "textarea":
    default:
      return String(value || "");
  }
}
