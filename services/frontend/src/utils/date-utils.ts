import { TFunction } from "@/types";

// Common date and time utilities that can be reused across the application

export type DayOption = {
  value: string;
  label: string;
};

export const getWeekendDayOptions = (t: TFunction): DayOption[] => [
  { value: "0", label: t("common.days.sunday") || "Sunday" },
  { value: "1", label: t("common.days.monday") || "Monday" },
  { value: "2", label: t("common.days.tuesday") || "Tuesday" },
  { value: "3", label: t("common.days.wednesday") || "Wednesday" },
  { value: "4", label: t("common.days.thursday") || "Thursday" },
  { value: "5", label: t("common.days.friday") || "Friday" },
  { value: "6", label: t("common.days.saturday") || "Saturday" },
];

export const normalizeWeekendDays = (values: string[]): string[] =>
  values.map((day) => String(parseInt(day, 10)));
