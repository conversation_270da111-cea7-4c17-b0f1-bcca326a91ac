import { cn } from "@/lib/utils";
import React from "react";
import type { JSX } from "react";

/**
 * Utility functions for dynamic input width and dots generation
 * Can be reused across different input components
 */

//  Calculate dynamic input width based on content length
export const calculateDynamicWidth = (
  value: string,
  options: {
    multiplier?: number;
    maxWidth?: number;
    minWidth?: number;
  } = {},
) => {
  const { multiplier = 0.98, maxWidth = 20, minWidth = 2 } = options;

  if (!value) return "4ch";

  const charWidth = value.length * multiplier;
  const calculatedWidth = Math.min(maxWidth, Math.max(charWidth, minWidth));
  return `${calculatedWidth}ch`;
};

//  Width in 'ch' units (e.g., "10ch")
export const generateWidthDots = (
  inputWidth: string,
  dotConfig: {
    dotSize?: string;
    dotColor?: string;
    gapSize?: number;
  } = {},
): JSX.Element[] => {
  const {
    dotSize = "w-1 h-1",
    dotColor = "bg-amber-600",
    gapSize = 6,
  } = dotConfig;

  // Convert ch to approximate px (1ch ≈ 8px)
  const widthInPx = parseFloat(inputWidth) * 8;
  const dotsCount = Math.floor(widthInPx / gapSize);

  return Array.from({ length: Math.max(dotsCount, 1) }, (_, i) => (
    <div key={i} className={cn(`${dotSize} ${dotColor} rounded-full`)} />
  ));
};

export const generateDisplayDots = (
  value: string,
  fieldType: string,
  calculateWidth: (value: string) => string,
  generateDots: (width: string) => JSX.Element[],
): JSX.Element[] => {
  if (fieldType === "text") {
    // For currency fields, use only numeric part
    const numericPart = value.replace(/[^\d,]/g, "");
    const numberWidth = calculateWidth(numericPart);
    return generateDots(numberWidth);
  } else {
    // For other fields, use full value width
    const valueWidth = calculateWidth(value);
    return generateDots(valueWidth);
  }
};

export const getCurrencySymbol = (
  translations: (key: string) => string,
  isMobile: boolean,
): string => {
  const prefixCurrencyConfig = `common.currencyConfig.${isMobile ? "mobile" : "desktop"}`;
  return translations(`${prefixCurrencyConfig}.symbol`);
};

/**
 * Configuration for dynamic input styling
 */
export const DYNAMIC_INPUT_CONFIG = {
  WIDTH_MULTIPLIER: 0.98,
  MAX_WIDTH_CH: 20,
  MIN_WIDTH_CH: 2,
  MAX_WIDTH_REM: "9rem",

  // Dots styling
  DOT_SIZE: "w-[3px] h-[3px]",
  DOT_COLOR: "bg-amber-600",
  DOT_GAP: 6,

  // Colors
  AMBER_TEXT: "text-amber-600",
  GRAY_TEXT: "text-gray-900",

  // Positioning
  DOTS_BOTTOM_OFFSET: "-bottom-1",
} as const;

//  Generate dynamic input wrapper styles
export const getDynamicInputStyles = (
  width: string,
  maxWidth: string = DYNAMIC_INPUT_CONFIG.MAX_WIDTH_REM,
) => ({
  width,
  maxWidth,
});

export const getDotsContainerClasses = (
  fieldType: string,
  isEditing: boolean = false,
): string => {
  const baseClasses = `absolute ${DYNAMIC_INPUT_CONFIG.DOTS_BOTTOM_OFFSET} flex justify-between`;

  if (isEditing) {
    return `${baseClasses} end-0 start-0`;
  }

  if (fieldType === "text") {
    return `${baseClasses} start-0`;
  }

  return `${baseClasses} !left-0`;
};
