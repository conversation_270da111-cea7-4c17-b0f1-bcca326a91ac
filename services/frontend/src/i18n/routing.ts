import { defineRouting } from "next-intl/routing";
import { createNavigation } from "next-intl/navigation";
import { LANGUAGES } from "@/constants/enum";

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: [LANGUAGES.ENGLISH, LANGUAGES.ARABIC],

  // Used when no locale matches
  defaultLocale: LANGUAGES.ARABIC,
});

export type Locale = (typeof routing.locales)[number];

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
