import { buildApiUrl } from "@/utils/api";
import { BaseAPI } from ".";
import { cookies } from "next/headers";

/**
 * ProcureService class for handling all procure-related API requests
 */
class ProcureService extends BaseAPI {
  constructor() {
    super(buildApiUrl("procure"));
  }

  // Generic request method
  async requestData<T>(
    endpoint: string,
    method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
    body?: any,
    headersOverride?: HeadersInit,
  ): Promise<T> {
    const procureSessionToken = await getProcureSessionToken();

    const headers: HeadersInit = {
      Authorization: `Bearer ${procureSessionToken}`,
      "Content-Type": "application/json", // Default content type
      ...headersOverride, // Allows overriding default headers
    };

    const config: RequestInit = {
      method,
      headers,
    };

    if (body) {
      config.body = JSON.stringify(body);
    }

    const response = await this.request<T>(endpoint, config);
    return response;
  }
}

// Create a singleton instance
export const procureService = new ProcureService();

export const getProcureSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("procure_session_token")?.value || null;
};
