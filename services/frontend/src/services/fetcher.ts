import {
  notifyTokenExpired,
  isTokenExpired,
} from "@/lib/token-expiration-manager";
import { extractApiError } from "@/utils/api-error-extractor";

export const fetcher = async (url: string) => {
  const response = await fetch(url);

  // Handle authentication errors
  if (response.status === 401) {
    if (!isTokenExpired()) {
      notifyTokenExpired();
    }

    throw new Error("Unauthorized");
  }

  if (!response.ok) {
    // Handle redirects
    if (response.status === 307 || response.status === 308) {
      const location = response.headers.get("Location");
      if (location) {
        window.location.href = location;
        return;
      }
    }

    // Parse error response
    let errorData = null;
    try {
      errorData = await response.json();
    } catch (parseError) {
      // If parsing fails, we'll handle it in extractApiError
    }

    // Create error object with response data for extraction
    const errorObj = {
      message: errorData?.error || errorData?.message,
      error: errorData?.error,
      response: {
        status: response.status,
        statusText: response.statusText,
        data: errorData,
      },
      status: response.status,
      data: errorData,
    };

    // Use the centralized error extraction utility
    const { message: errorMessage, statusCode } = extractApiError(errorObj);

    const error = new Error(errorMessage);
    // Attach extra info to the error object
    (error as any).status = statusCode;
    (error as any).statusCode = statusCode;
    (error as any).data = errorData;

    throw error;
  }

  const data = await response.json();
  console.log("🔍 FETCHER returning data:", data);
  return data;
};
