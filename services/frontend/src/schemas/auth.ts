import { TFunction } from "@/types";
import { z } from "zod";
import { emailValidation, stringValidation } from "./common-validations";

export const loginSchema = (t: TFunction) => {
  return z.object({
    email: emailValidation(t).min(1, t("common.form.email.error.required")),
    password: stringValidation(t, "Password").min(
      1,
      t("common.form.password.error.required"),
    ),
    image: z.string().optional(),
  });
};

export type loginSchemaType = z.infer<ReturnType<typeof loginSchema>>;

export const ForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: emailValidation(t).min(1, t("common.form.email.error.required")),
  });
};

export type ForgotPasswordSchemaType = z.infer<
  ReturnType<typeof ForgotPasswordSchema>
>;
export const ResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .nonempty(t("common.form.password.error.required"))
        .min(6, t("common.form.password.error.length")),
      confirmPassword: z
        .string()
        .nonempty(t("common.form.confirmPassword.error.required"))
        .min(6, t("common.form.password.error.length")),
    })
    .refine((obj) => obj.password === obj.confirmPassword, {
      message: t("common.form.password.error.match"),
      path: ["confirmPassword"],
    });
};
export type ResetPasswordSchemaType = z.infer<
  ReturnType<typeof ResetPasswordSchema>
>;

export const verifyCodeSchema = (t: TFunction) => {
  return z.object({
    code: z
      .string()
      .min(1, t("common.form.verifyCode.error.required"))
      .regex(/^\d+$/, t("common.form.verifyCode.error.notNumber")),
  });
};

export type verifyCodeSchemaType = z.infer<ReturnType<typeof verifyCodeSchema>>;
