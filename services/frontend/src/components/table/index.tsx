"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  RowSelectionState,
  SortingState,
  TableMeta,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Table, TableBody, TableRow, TableCell } from "@/components/ui/table";
import React, { useEffect, useRef, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Directions, LANGUAGES } from "@/constants/enum";
import { cn } from "@/lib/utils";
import DataTableHeader from "./table-header";
import { TableHeaderControls } from "./table-header-controls";
import { useSearchParams } from "next/navigation";
import { SkeletonTable } from "../skeletons";
import { Locale } from "@/i18n/routing";
import { EnhancedExportControls } from "./enhanced-export-controls";
import type { DataTableExportConfig } from "@/types/export";
import { getExportFilters } from "@/utils/export-filters";

interface DataTableProps<TData extends Record<string, unknown>, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  tableHeadStyle?: string;
  tableCellStyle?: string;
  meta?: TableMeta<TData>;
  tableContainerClass?: string;
  rowSelection?: RowSelectionState;
  onRowSelectionChange: (
    updater:
      | RowSelectionState
      | ((prev: RowSelectionState) => RowSelectionState),
  ) => void;
  translationPrefix: string;
  tableId: string;
  isLoading?: boolean;
  dataCount?: number;
  initialLimit?: number;
  error?: Error | null;
  hideSearch?: boolean;
  hideFilters?: boolean;
  hideColumns?: boolean;
  hideTableHeader?: boolean;
  // Add new prop for custom header actions
  headerActions?: React.ReactNode;
  // Export configuration
  exportConfig?: DataTableExportConfig;
  onRowClick?: (row: TData) => void;
}
export function DataTable<TData extends Record<string, unknown>, TValue>({
  columns,
  data,
  dataCount,
  initialLimit,
  error,
  title,
  tableHeadStyle,
  tableCellStyle,
  meta,
  tableContainerClass,
  rowSelection,
  translationPrefix,
  tableId,
  onRowSelectionChange,
  isLoading,
  hideSearch = false,
  hideFilters = false,
  hideColumns = false,
  hideTableHeader = false,
  headerActions,
  exportConfig,
  onRowClick,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? initialLimit ?? 3;
  const locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  const searchInputRef = useRef<HTMLInputElement>(null);



  const table = useReactTable({
    data,
    columns,
    meta: { t, locale, ...(meta || {}) },
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    // Only use client-side sorting if no backend sort is applied
    // Backend sorting is handled via API parameters, so we respect that order
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange,
    enableRowSelection: true,
    getRowId: (row: TData) => row.id as string,
    // Disable manual sorting to respect backend sort order
    enableSorting: false,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Prepare export filters from current table state
  const exportFilters = getExportFilters(
    columnFilters,
    sorting,
    searchParams,
    exportConfig,
  );

  // Combine header actions with export controls
  const combinedHeaderActions = () => {
    const actions = [];

    // Add export controls if configured
    if (exportConfig?.enabled !== false && exportConfig?.entity) {
      actions.push(
        <EnhancedExportControls
          key="enhanced-export"
          entity={exportConfig.entity}
          filters={exportFilters}
          disabled={isLoading || data.length === 0}
          tableColumns={columns}
          exportColumns={exportConfig.exportColumns}
          columnCategories={exportConfig.columnCategories}
          columnDescriptions={exportConfig.columnDescriptions}
          requiredColumns={exportConfig.requiredColumns}
          excludeColumns={exportConfig.excludeColumns}
          defaultSelectedColumns={exportConfig.defaultSelectedColumns}
        />,
      );
    }

    // Add custom header actions
    if (headerActions) {
      const wrppedHeaderActions = React.isValidElement(headerActions) ? (
        <React.Fragment key="custom-header-actions">
          {headerActions}
        </React.Fragment>
      ) : (
        headerActions
      );
      actions.push(wrppedHeaderActions);
    }

    return actions.length > 0 ? (
      <div className="flex items-center gap-2">{actions}</div>
    ) : undefined;
  };



    useEffect(() => {
    if (!isLoading && searchInputRef.current) {
      const el = searchInputRef.current;
      el.focus();
      const pos = el.value.length;
      el.setSelectionRange(pos, pos);
    }
  }, [isLoading]);

  return (
    <div
      className={cn("flex flex-col flex-1 relative z-10", tableContainerClass)}
    >
      {!hideTableHeader && (
        <TableHeaderControls
          isLoading={isLoading!}
          table={table}
          title={title}
          translationPrefix={translationPrefix}
          tableId={tableId}
          hideSearch={hideSearch}
          hideFilters={hideFilters}
          hideColumns={hideColumns}
          headerActions={combinedHeaderActions()}
          searchInputProps={{ id: `${tableId}-search-input` }}
      
        />
      )}
      <div className="rounded-xl border overflow-x-auto custom-scroll border-[#F3F4F6] min-h-full flex-1 flex items-start">
        {error ? (
          <div className="p-4 flex items-center justify-center flex-1">
            <span className="text-error text-center">{error.message}</span>
          </div>
        ) : isLoading ? (
          <SkeletonTable
            wrapperClass="flex-1"
            rowCount={Math.max(data?.length ?? 0, Number(limit))}
          />
        ) : (
          <Table
            dir={isAr ? Directions.RTL : Directions.LTR}
            lang={locale}
            className="border-none max-md:min-w-[776px] md:min-h-full"
          >
            <DataTableHeader table={table} tableHeadStyle={tableHeadStyle} />
            <TableBody className="text-center">
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, idx) => (
                  <TableRow
                    className={`h-[60px] border-none pr-0 ${
                      onRowClick ? "cursor-pointer" : ""
                    }`}
                    key={`${row.id}-${idx}`}
                    data-state={row.getIsSelected() && "selected"}
                    onClick={() => {
                      if (onRowClick) {
                        onRowClick(row.original);
                      }
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={cn(
                          "text-center text-sm font-semibold leading-[140%]",
                          tableCellStyle,
                          cell.column.id === "status" && "",
                          cell.column.id === "description" &&
                            "text-xs font-normal",
                        )}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="text-center">
                    {t("common.Table.noResults")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
