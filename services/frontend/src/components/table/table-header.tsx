import { flexRender } from "@tanstack/react-table";

import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Table } from "@tanstack/react-table";

const DataTableHeader = <TData, _>({
  table,
  tableHeadStyle,
}: {
  table: Table<TData>;
  tableHeadStyle?: string;
}) => {
  return (
    <TableHeader className="h-[60px]">
      {table.getHeaderGroups().map((headerGroup) => {
        return (
          <TableRow
            key={headerGroup.id}
            className="border-none text-[#ACB5BB] font-semibold text-sm"
          >
            {headerGroup.headers.map((header) => {
              return (
                <TableHead
                  key={header.id}
                  className={`text-center bg-[#F9FAFB] text-[#ACB5BB] font-semibold ${tableHeadStyle}`}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHead>
              );
            })}
          </TableRow>
        );
      })}
    </TableHeader>
  );
};

export default DataTableHeader;
