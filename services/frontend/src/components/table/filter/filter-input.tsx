import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TFunction } from "@/types";
import { FilterRule, ColumnWithMeta } from "../types";
import { filterTypeManager } from "@/lib/filter-type-manager";

type FilterInputProps<TData> = {
  rule: FilterRule;
  column: ColumnWithMeta<TData> | undefined;
  updateRule: (
    groupId: string,
    ruleId: string,
    field: keyof FilterRule,
    value: any, // Support all types: string, Date, boolean, number, etc.
  ) => void;
  t: TFunction;
  groupId: string;
  translationPrefix?: string;
};

export function FilterInput<TData>({
  rule,
  column,
  updateRule,
  t,
  groupId,
  translationPrefix,
}: FilterInputProps<TData>) {
  // Get the column meta data
  const meta = column?.columnDef?.meta;

  // Determine the column type using FilterTypeManager
  let columnType = "text";

  // First try to get type from FilterTypeManager if we have translationPrefix
  if (translationPrefix && rule.field) {
    const fieldConfig = filterTypeManager.getFieldConfig(translationPrefix, rule.field);
    if (fieldConfig) {
      columnType = fieldConfig.filterType;
    } else if (meta?.filterType) {
      // Fallback to meta data if FilterTypeManager doesn't have the field
      columnType = meta.filterType;
    }
  } else if (meta?.filterType) {
    // Use meta data if no translationPrefix
    columnType = meta.filterType;
  }

  // Use the determined column type
  if (columnType === "date") {
    return (
      <DatePicker
        date={rule.value instanceof Date ? rule.value : undefined}
        setDate={(date) => updateRule(groupId, rule.id, "value", date || "")}
        placeholder={t("common.Table.filter-options.selectDate")}
        className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm"
      />
    );
  }

  // For number type, use number input
  if (columnType === "number") {
    return (
      <Input
        type="number"
        value={typeof rule.value === "string" ? rule.value : ""}
        onChange={(e) => updateRule(groupId, rule.id, "value", e.target.value)}
        placeholder={t("common.Table.filter-options.enterValue")}
        className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm"
      />
    );
  }

  // For boolean type, use select dropdown
  if (columnType === "boolean") {
    return (
      <Select
        value={String(rule.value)}
        onValueChange={(value) => updateRule(groupId, rule.id, "value", value)}
      >
        <SelectTrigger className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm">
          <SelectValue placeholder={t("common.Table.filter-options.selectValue")} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="true">{t("common.true")}</SelectItem>
          <SelectItem value="false">{t("common.false")}</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // For other types, use regular text input
  return (
    <Input
      value={typeof rule.value === "string" ? rule.value : ""}
      onChange={(e) => updateRule(groupId, rule.id, "value", e.target.value)}
      placeholder={t("common.Table.filter-options.enterValue")}
      className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm mt-0"
    />
  );
}
