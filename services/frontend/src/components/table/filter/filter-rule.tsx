import { Table } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";
import { TFunction } from "@/types";
import { FilterRule, TFilterGroup } from "../types";
import { FilterInput } from "./filter-input";
import {
  getColumnType,
  getFilterableColumns,
  getOperators,
} from "./filter-utils";

interface FilterRuleProps<TData> {
  rule: FilterRule;
  table: Table<TData>;
  translationPrefix: string;
  onRemove: () => void;
  onFieldChange: (value: string) => void;
  onOperatorChange: (value: string) => void;
  t: TFunction;
  group: TFilterGroup;
  updateRule: (
    groupId: string,
    ruleId: string,
    field: keyof FilterRule,
    value: any, // Support all types: string, Date, boolean, number, etc.
  ) => void;
}

export function FilterRuleComponent<TData>({
  rule,
  table,
  translationPrefix,
  onRemove,
  onFieldChange,
  onOperatorChange,
  t,
  group,
  updateRule,
}: FilterRuleProps<TData>) {
  return (
    <div className="grid grid-cols-[1fr_1fr_auto] md:grid-cols-[1fr_1fr_1fr_auto] gap-3 py-2 bg-gray-50 rounded-lg border border-gray-100">
      <Select value={rule.field} onValueChange={onFieldChange}>
        <SelectTrigger className="w-full h-10 bg-white border-gray-200 rounded-md font-normal text-sm">
          <SelectValue
            placeholder={t("common.Table.filter-options.selectField")}
          />
        </SelectTrigger>
        <SelectContent onPointerDownOutside={(e) => e.stopPropagation()}>
          {getFilterableColumns(table).map(
            (column) => (
              <SelectItem key={column.id} value={column.id}>
                {t(`${translationPrefix}.columns.${String(column.id)}`)}
              </SelectItem>
            ),
          )}
        </SelectContent>
      </Select>

      {rule.field && (
        <>
          <Select value={rule.operator} onValueChange={onOperatorChange}>
            <SelectTrigger className="w-full h-10 bg-white border-gray-200 rounded-md font-normal text-sm">
              <SelectValue
                placeholder={t("common.Table.filter-options.selectOperator")}
              />
            </SelectTrigger>
            <SelectContent onPointerDownOutside={(e) => e.stopPropagation()}>
              {getOperators(getColumnType(rule.field, table), t).map((op) => (
                <SelectItem key={op.value} value={op.value}>
                  {op.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <FilterInput
            rule={rule}
            column={table.getAllColumns().find((col) => col.id === rule.field)}
            updateRule={updateRule}
            t={t}
            groupId={group.id}
            translationPrefix={translationPrefix}
          />
        </>
      )}

      {group.rules.length > 1 && (
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full hover:bg-red-50 hover:text-red-500 transition-colors"
          onClick={onRemove}
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
