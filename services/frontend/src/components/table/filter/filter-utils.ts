import { Table } from "@tanstack/react-table";
import { TFilterGroup, FilterRule, FilterableColumn } from "../types";
import { TFunction } from "@/types";





export const getColumnType = <TData>(
  field: string,
  table: Table<TData>,
): string => {

  // Check if the field name contains specific keywords to determine type
  if (field.toLowerCase().includes("date")) {
    return "date";
  } else if (
    field.toLowerCase().includes("total") ||
    field.toLowerCase().includes("hours") ||
    field.toLowerCase().includes("salary") ||
    field.toLowerCase().includes("duration") ||
    field.toLowerCase().includes("amount") ||
    field.toLowerCase().includes("count")
  ) {
    return "number";
  }

  // Check if the column exists in the table's getAllColumns() result
  // This avoids directly calling getColumn() which throws an error
  const allColumns = table.getAllColumns();
  const matchingColumn = allColumns.find((col) => col.id === field);

  if (matchingColumn) {
    // The column exists, try to get its type from meta
    // We need to use a type assertion here because the Column type from tanstack/react-table
    // doesn't expose columnDef.meta in its type definition
    const columnDef = (
      matchingColumn as unknown as {
        columnDef?: { meta?: { filterType?: string } };
      }
    )?.columnDef;
    if (columnDef?.meta?.filterType) {
      return columnDef.meta.filterType;
    }
  }

  // Default to text type if no type is found
  return "text";
};

export const getFilterableColumns = <TData>(
  table: Table<TData>,
) => {
  // Use dynamic columns from the table
  return table
    .getAllColumns()
    .filter(
      (column) =>
        column.getCanFilter() &&
        column.id !== "select" &&
        column.id !== "actions" &&
        column.id !== "number" &&
        column.id !== "id",
    );
};

export const getOperators = (columnType: string, t: TFunction) => {
  switch (columnType) {
    case "date":
      return [
        { value: "eq", label: t("common.Table.filter-operators.eq") },
        { value: "not_eq", label: t("common.Table.filter-operators.not_eq") },
        { value: "lt", label: t("common.Table.filter-operators.lt") },
        { value: "lteq", label: t("common.Table.filter-operators.lteq") },
        { value: "gt", label: t("common.Table.filter-operators.gt") },
        { value: "gteq", label: t("common.Table.filter-operators.gteq") },
        { value: "null", label: t("common.Table.filter-operators.null") },
        { value: "not_null", label: t("common.Table.filter-operators.not_null") },
        { value: "present", label: t("common.Table.filter-operators.present") },
        { value: "blank", label: t("common.Table.filter-operators.blank") },
      ];
    case "number":
      return [
        { value: "eq", label: t("common.Table.filter-operators.eq") },
        { value: "not_eq", label: t("common.Table.filter-operators.not_eq") },
        { value: "lt", label: t("common.Table.filter-operators.lt") },
        { value: "lteq", label: t("common.Table.filter-operators.lteq") },
        { value: "gt", label: t("common.Table.filter-operators.gt") },
        { value: "gteq", label: t("common.Table.filter-operators.gteq") },
        { value: "null", label: t("common.Table.filter-operators.null") },
        { value: "not_null", label: t("common.Table.filter-operators.not_null") },
        { value: "present", label: t("common.Table.filter-operators.present") },
        { value: "blank", label: t("common.Table.filter-operators.blank") },
      ];
    case "boolean":
      return [
        { value: "true", label: t("common.Table.filter-operators.true") },
        { value: "false", label: t("common.Table.filter-operators.false") },
        { value: "null", label: t("common.Table.filter-operators.null") },
        { value: "not_null", label: t("common.Table.filter-operators.not_null") },
      ];
    default:
      return [
        { value: "eq", label: t("common.Table.filter-operators.eq") },
        { value: "not_eq", label: t("common.Table.filter-operators.not_eq") },
        { value: "cont", label: t("common.Table.filter-operators.cont") },
        { value: "not_cont", label: t("common.Table.filter-operators.not_cont") },
        { value: "i_cont", label: t("common.Table.filter-operators.i_cont") },
        { value: "not_i_cont", label: t("common.Table.filter-operators.not_i_cont") },
        { value: "start", label: t("common.Table.filter-operators.start") },
        { value: "not_start", label: t("common.Table.filter-operators.not_start") },
        { value: "end", label: t("common.Table.filter-operators.end") },
        { value: "not_end", label: t("common.Table.filter-operators.not_end") },
        { value: "matches", label: t("common.Table.filter-operators.matches") },
        { value: "does_not_match", label: t("common.Table.filter-operators.does_not_match") },
        { value: "null", label: t("common.Table.filter-operators.null") },
        { value: "not_null", label: t("common.Table.filter-operators.not_null") },
        { value: "present", label: t("common.Table.filter-operators.present") },
        { value: "blank", label: t("common.Table.filter-operators.blank") },
      ];
  }
};

export const evaluateRule = <TData extends Record<string, unknown>>(
  rule: FilterRule,
  row: TData,
): boolean => {
  const value = row[rule.field];
  const ruleValue = rule.value;

  // Handle null checks first
  if (rule.operator === "null") {
    return value === null || value === undefined || value === "";
  }
  if (rule.operator === "not_null") {
    return value !== null && value !== undefined && value !== "";
  }

  // For other operators, if value is null/undefined, return false
  if (value === null || value === undefined) return false;

  switch (rule.operator) {
    case "cont":
      return String(value)
        .toLowerCase()
        .includes(String(ruleValue).toLowerCase());
    case "not_cont":
      return !String(value)
        .toLowerCase()
        .includes(String(ruleValue).toLowerCase());
    case "eq":
      return typeof value === "number"
        ? Number(value) === Number(ruleValue)
        : String(value).toLowerCase() === String(ruleValue).toLowerCase();
    case "not_eq":
      return typeof value === "number"
        ? Number(value) !== Number(ruleValue)
        : String(value).toLowerCase() !== String(ruleValue).toLowerCase();
    case "start":
      return String(value)
        .toLowerCase()
        .startsWith(String(ruleValue).toLowerCase());
    case "end":
      return String(value)
        .toLowerCase()
        .endsWith(String(ruleValue).toLowerCase());
    case "gt":
      return Number(value) > Number(ruleValue);
    case "gteq":
      return Number(value) >= Number(ruleValue);
    case "lt":
      return Number(value) < Number(ruleValue);
    case "lteq":
      return Number(value) <= Number(ruleValue);
    default:
      return false;
  }
};

export const applyFiltersToData = <TData extends Record<string, unknown>>(
  data: TData[],
  groups: TFilterGroup[],
): TData[] => {
  if (groups.length === 0 || !groups[0] || groups[0].rules.length === 0)
    return data;

  // Get valid rules from the first group (we only have one group now)
  const validRules = groups[0].rules.filter(
    (rule) => rule.field && rule.operator && rule.value,
  );

  if (validRules.length === 0) return data;

  return data.filter((row) => {
    // Check if all rules are satisfied (AND between rules)
    return validRules.every((rule) => evaluateRule(rule, row));
  });
};
