import { filterTypeManager } from "@/lib/filter-type-manager";

export interface RansackFilter<PERSON>ey {
  field: string;
  operator: string;
  originalKey: string;
}

export const parseRansackKey = (key: string): RansackFilterKey | null => {
  const fieldMatch = key.match(/^(.+)_([^_]+)$/);

  if (!fieldMatch) {
    return null;
  }

  const [, field, operator] = fieldMatch;

  return {
    field,
    operator,
    originalKey: key,
  };
};

export const createRansackKey = (field: string, operator: string): string => {
  return `${field}_${operator}`;
};

export const isValidRansackKey = (key: string): boolean => {
  return parseRansackKey(key) !== null;
};

export const extractRansackFilters = (
  params: Record<string, any>,
): RansackFilterKey[] => {
  return Object.keys(params)
    .map((key) => parseRansackKey(key))
    .filter((parsed): parsed is RansackFilter<PERSON>ey => parsed !== null);
};

export const RANSACK_OPERATORS = {
  eq: "equals", not_eq: "not equals",
  lt: "less than", lteq: "less than or equal",
  gt: "greater than", gteq: "greater than or equal",
  matches: "matches", does_not_match: "does not match",
  start: "starts with", not_start: "does not start with",
  end: "ends with", not_end: "does not end with",
  cont: "contains", not_cont: "does not contain",
  i_cont: "contains (case insensitive)", not_i_cont: "does not contain (case insensitive)",
  null: "is null", not_null: "is not null",
  present: "is present", blank: "is blank",
  true: "is true", false: "is false",
} as const;

export type RansackOperator = keyof typeof RANSACK_OPERATORS;

export const getOperatorDescription = (operator: string): string => {
  return RANSACK_OPERATORS[operator as RansackOperator] || operator;
};

export const isArrayOperator = (operator: string): boolean => {
  const arrayOperators = [
    'in', 'not_in', 'matches_any', 'matches_all', 'does_not_match_any', 'does_not_match_all',
    'lt_any', 'lteq_any', 'gt_any', 'gteq_any', 'lt_all', 'lteq_all', 'gt_all', 'gteq_all',
    'start_any', 'start_all', 'not_start_any', 'not_start_all',
    'end_any', 'end_all', 'not_end_any', 'not_end_all',
    'cont_any', 'cont_all', 'not_cont_any', 'not_cont_all',
    'i_cont_any', 'i_cont_all', 'not_i_cont_any', 'not_i_cont_all', 'not_eq_all'
  ];
  return arrayOperators.includes(operator);
};

export const processRansackFilters = (
  parsedFilters: Record<string, any>,
  tablePrefix: string,
): string[] => {
  const filterParams: string[] = [];

  Object.entries(parsedFilters).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);

    if (parsedKey && isArrayOperator(parsedKey.operator)) return;

    if (Array.isArray(value)) {
      value.forEach(val => {
        const formattedValue = parsedKey
          ? filterTypeManager.formatValue(val, tablePrefix, parsedKey.field)
          : String(val);
        filterParams.push(`filter[${key}]=${formattedValue}`);
      });
    } else {
      const formattedValue = parsedKey
        ? filterTypeManager.formatValue(value, tablePrefix, parsedKey.field)
        : String(value);
      filterParams.push(`filter[${key}]=${formattedValue}`);
    }
  });

  return filterParams;
};

export const applyRansackFiltersToParams = (
  parsedFilters: Record<string, any>,
  tablePrefix: string,
  params: URLSearchParams,
): void => {
  Object.entries(parsedFilters).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);

    if (parsedKey && isArrayOperator(parsedKey.operator)) return;

    if (Array.isArray(value)) {
      value.forEach(val => {
        const formattedValue = parsedKey
          ? filterTypeManager.formatValue(val, tablePrefix, parsedKey.field)
          : String(val);
        params.append(`filter[${key}]`, formattedValue);
      });
    } else {
      const formattedValue = parsedKey
        ? filterTypeManager.formatValue(value, tablePrefix, parsedKey.field)
        : String(value);
      params.set(`filter[${key}]`, formattedValue);
    }
  });
};

// Universal helper functions that ALL components use - Zero Component Type Logic
export const parseFilterValue = (
  value: any,
  tableId: string,
  fieldId: string
): any => {
  return filterTypeManager.parseValue(value, tableId, fieldId);
};

export const formatFilterValue = (
  value: any,
  tableId: string,
  fieldId: string
): string => {
  const result = filterTypeManager.formatValue(value, tableId, fieldId);
  return result;
};

// Batch processing for multiple values with array support
export const parseFilterValues = (
  filters: Record<string, any>,
  tableId: string
): Record<string, any> => {
  const parsed: Record<string, any> = {};

  Object.entries(filters).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);
    const fieldId = parsedKey?.field || key;

    if (isArrayOperator(parsedKey?.operator || '')) return; // Skip array operators

    if (Array.isArray(value)) {
      parsed[key] = value.map(v => parseFilterValue(v, tableId, fieldId));
    } else {
      parsed[key] = parseFilterValue(value, tableId, fieldId);
    }
  });

  return parsed;
};

export const formatFilterValues = (
  filters: Record<string, any>,
  tableId: string
): Record<string, any> => {
  const formatted: Record<string, any> = {};

  Object.entries(filters).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);
    const fieldId = parsedKey?.field || key;

    if (isArrayOperator(parsedKey?.operator || '')) {
      return; // Skip array operators
    }

    if (Array.isArray(value)) {
      formatted[key] = value.map(v => formatFilterValue(v, tableId, fieldId));
    } else {
      const formattedValue = formatFilterValue(value, tableId, fieldId);
      formatted[key] = formattedValue;
    }
  });

  return formatted;
};
