import { Table, Column } from "@tanstack/react-table";

export type CustomColumnMeta = {
  filterType?: "text" | "date" | "number" | "boolean";
};

// type for manually defined filterable column
export type FilterableColumn = {
  id: string;
  filterType: "text" | "date" | "number" | "boolean";
};

export type FilterModalProps<TData> = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  table: Table<TData>;
  translationPrefix: string;
  tableId: string; // For FilterTypeManager
};

export type FilterRule = {
  id: string;
  field: string;
  operator: string;
  value: any; // Support all types: string, Date, boolean, number, etc.
};

export type TFilterGroup = {
  id: string;
  rules: FilterRule[];
};

// Extend the Column type with our custom meta
export type ColumnWithMeta<TData> = Column<TData> & {
  columnDef: {
    meta?: CustomColumnMeta;
  };
};
