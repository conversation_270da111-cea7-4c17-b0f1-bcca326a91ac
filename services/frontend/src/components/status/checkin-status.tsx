import { Attendance_Event_type } from "@/constants/enum";

const statusBackgroundColors = {
  [Attendance_Event_type.CheckIn]: "bg-success-50",
  [Attendance_Event_type.CheckOut]: "bg-red-50",
};

const statusTextColors = {
  [Attendance_Event_type.CheckIn]: "text-green-900",
  [Attendance_Event_type.CheckOut]: "text-red-500",
};

const CheckinStatus = ({
  status,
  label,
}: {
  status: Attendance_Event_type;
  label: string;
}) => {
  // Default to neutral styling if status is not recognized
  const bgColor = statusBackgroundColors[status] || "bg-neutral-100";
  const textColor = statusTextColors[status] || "text-neutral-600";

  return (
    <div
      className={`min-w-24 max-w-24 min-h-[27px] text-center leading-[27px] px-3 rounded-sm text-sm font-semibold tracking-tight ${bgColor} ${textColor}`}
    >
      {label}
    </div>
  );
};

export default CheckinStatus;
