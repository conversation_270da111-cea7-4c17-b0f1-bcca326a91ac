import { DEVICE_STATUS } from "@/constants/enum";

const statusBackgroundColors = {
  [DEVICE_STATUS.ACTIVE]: "bg-success-50",
  [DEVICE_STATUS.INACTIVE]: "bg-red-50",
  [DEVICE_STATUS.MAINTENANCE]: "bg-yellow-50",
  [DEVICE_STATUS.ERROR]: "bg-red-50",
};

const statusTextColors = {
  [DEVICE_STATUS.ACTIVE]: "text-green-900",
  [DEVICE_STATUS.INACTIVE]: "text-red-500",
  [DEVICE_STATUS.MAINTENANCE]: "text-yellow-700",
  [DEVICE_STATUS.ERROR]: "text-red-600",
};

const DeviceStatus = ({
  status,
  label,
  statusStyle = "",
}: {
  status: DEVICE_STATUS;
  label: string;
  statusStyle?: string;
}) => {
  // Default to neutral styling if status is not recognized
  const bgColor = statusBackgroundColors[status] || "bg-neutral-100";
  const textColor = statusTextColors[status] || "text-neutral-600";

  return (
    <div
      className={`min-w-24 max-w-24 min-h-[27px] text-center leading-[27px] px-3 rounded-sm text-sm font-semibold tracking-tight ${bgColor} ${textColor} ${statusStyle}`}
    >
      {label}
    </div>
  );
};

export default DeviceStatus;
