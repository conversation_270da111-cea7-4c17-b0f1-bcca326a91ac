"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useLocale, useTranslations } from "next-intl";
import dynamic from "next/dynamic";
const DocViewer = dynamic(() => import("@cyntler/react-doc-viewer"), {
  ssr: false,
});
import "@cyntler/react-doc-viewer/dist/index.css";
import "@/styles/doc-viewer.css";
import { DocViewerRenderers } from "@cyntler/react-doc-viewer";
import { Locale } from "@/i18n/routing";
export type FilePreviewProps = {
  fileName: string;
  fileUrl: string;
  isOpen: boolean;
  onClose: () => void;
  fileType?: string;
};

export default function FilePreview({
  fileName,
  fileUrl,
  isOpen,
  onClose,
  fileType,
}: FilePreviewProps) {
  const t = useTranslations();
  const [error, setError] = useState<string | null>(null);
  const locale = useLocale() as Locale;

  const isImage = React.useMemo(() => {
    if (fileType && fileType.startsWith("image/")) {
      return true;
    }

    const imageExtensions = [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".webp",
      ".svg",
    ];
    return imageExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
  }, [fileName, fileType]);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      setTimeout(() => setError(null), 300);
    }
  };

  const renderPreview = () => {
    const docs = [{ uri: fileUrl }];

    return (
      <div className="doc-container">
        <DocViewer
          documents={docs}
          pluginRenderers={DocViewerRenderers}
          className={`bg-white ${
            isImage
              ? "flex justify-center items-center w-[561px]"
              : "!h-[calc(100vh-150px)] !w-screen overflow-x-auto"
          }`}
          config={{
            header: {
              disableHeader: true,
              disableFileName: true,
              retainURLParams: false,
            },
            pdfZoom: { zoomJump: 0.2, defaultZoom: 0.9 },
            pdfVerticalScrollByDefault: false,
          }}
          theme={{
            primary: "",
            secondary: "#ffffff",
            tertiary: "hsl(var(--background-v2),0.5)",
            textPrimary: "#ffffff",
            textSecondary: "#fff",
            textTertiary: "#ffffff",
          }}
          language={locale}
        />
      </div>
    );
  };

  const errorContent = (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="text-center mb-6">
        <p className="text-lg font-medium mb-2 text-error">
          {t("common.preview.error.title")}
        </p>
        <p className="text-gray-500">{t("common.preview.error.description")}</p>
      </div>
    </div>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleOpenChange}
      closeBtnStyle="top-[22px]"
      className={
        isImage
          ? "max-h-[90vh] min-h-[50vh] md:max-w-[40vw]"
          : "max-h-full max-w-full p-0"
      }
      header={
        <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px] flex items-center justify-between">
          <span>{fileName}</span>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
              title={t("common.buttonText.cancel")}
            ></Button>
          </div>
        </DialogTitle>
      }
    >
      {error ? errorContent : renderPreview()}
    </ResponsiveDialog>
  );
}
