"use client";

import React, { useState, useMemo, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import debounce from "lodash/debounce";

interface SearchInputProps {
  placeholder?: string;
  onSearch: (value: string) => void;
  debounceTime?: number;
  className?: string;
}

export const MainSearchInput: React.FC<SearchInputProps> = ({
  placeholder,
  onSearch,
  debounceTime = 300,
  className = "",
}) => {
  const [value, setValue] = useState("");

  // Create a memoized debounced version of the onSearch callback.
  const debouncedSearch = useMemo(
    () =>
      debounce((val: string) => {
        onSearch(val);
      }, debounceTime),
    [onSearch, debounceTime],
  );

  // Cancel any pending debounced calls when unmounting.
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
    debouncedSearch(e.target.value);
  };

  return (
    <div className={`relative ${className}`}>
      <Input
        type="search"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className="rounded-full max-w-[283px] w-full ps-9 max-h-[38px] focus-visible:ring-secondary shadow-none bg-[#EFF0F0]"
      />
      <Search className="absolute top-1/2 -translate-y-1/2 start-3 w-[18px] h-[18px] text-secondary" />
    </div>
  );
};
