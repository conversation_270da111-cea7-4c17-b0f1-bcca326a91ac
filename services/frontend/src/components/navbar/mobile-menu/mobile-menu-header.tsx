"use client";

import React from "react";
import { SidebarData } from "@/types";
import { iconMap } from "@/constants";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUser } from "@/contexts/user-provider";
import { Skeleton } from "@/components/ui/skeleton";
import { MainSearchInput } from "../main-search-input";
import { useTranslations } from "next-intl";

interface MobileMenuHeaderProps {
  data: SidebarData;
}

const MobileMenuHeader: React.FC<MobileMenuHeaderProps> = ({ data }) => {
  const { user } = useUser();
  const t = useTranslations();

  // Handle search input changes
  const handleSearch = (value: string) => {
    console.log("User searched for:", value);
    // You can perform search logic here
  };

  return (
    <div className="space-y-6 pt-2">
      {/* User Profile Section */}
      <div className="flex items-center gap-3">
        {!user ? (
          <Skeleton className="h-[50px] w-[50px] rounded-full bg-gray-200" />
        ) : (
          <Avatar className="h-[50px] w-[50px] border-2 border-gray-200">
            <AvatarImage
              className="object-cover object-top"
              src={user.avatar || "/default-avatar.png"}
              alt="User Avatar"
            />
            <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
          </Avatar>
        )}

        <div className="flex flex-col">
          {!user ? (
            <>
              <Skeleton className="h-4 w-24 bg-gray-200 mb-1" />
              <Skeleton className="h-3 w-20 bg-gray-200" />
            </>
          ) : (
            <>
              <span className="font-bold text-sm text-secondary">
                {user?.name}
              </span>
            </>
          )}
        </div>
      </div>

      {/* System Header */}
      <div className="flex items-center gap-3">
        {data.icon &&
          iconMap[data.icon] &&
          React.createElement(iconMap[data.icon], {
            className: "w-6 h-6 text-secondary",
          })}
        <h2 className="font-bold text-xl text-secondary">{data.head}</h2>
      </div>

      {/* Search Input */}
      <MainSearchInput
        placeholder={t("common.navbar.search.placeholder")}
        onSearch={handleSearch}
        className="w-full hidden"
      />
    </div>
  );
};

export default MobileMenuHeader;
