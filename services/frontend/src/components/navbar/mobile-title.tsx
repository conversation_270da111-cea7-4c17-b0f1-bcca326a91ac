"use client";

import React from "react";

interface MobileTitleProps {
  currentTab: string | null;
  translatedTabs: string;
  translatedTitle: string;
}

/**
 * Component for mobile title
 */
const MobileTitle: React.FC<MobileTitleProps> = ({
  currentTab,
  translatedTabs,
  translatedTitle,
}) => (
  <div className="md:hidden container max-md:px-4 max-md:pt-6">
    <h2 className="font-bold text-lg text-secondary">
      {currentTab ? translatedTabs : translatedTitle}
    </h2>
  </div>
);

export default MobileTitle;
