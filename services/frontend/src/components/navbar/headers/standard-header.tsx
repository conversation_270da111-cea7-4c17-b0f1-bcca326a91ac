"use client";

import React from "react";
import Link from "next/link";
import { SidebarData } from "@/types";
import { iconMap } from "@/constants";

type StandardHeaderProps = {
  title: string;
  data: SidebarData;
  locale: string;
};

/**
 * Component for standard header (non-employee profile pages)
 */
const StandardHeader: React.FC<StandardHeaderProps> = ({
  title,
  data,
  locale,
}) => (
  <h1 className="font-bold text-[20px] text-secondary-2 me-2">
    <span className="max-md:hidden">{title}</span>
    <Link
      href={`/${locale}/${data.items ? data.items[0].url : ""}`}
      className="flex items-center gap-2 rtl:flex-row-reverse md:hidden"
    >
      <span className="font-bold">{data.head}</span>
      <span>
        {data.icon &&
          React.createElement(iconMap[data.icon], {
            className: "w-6 h-6",
          })}
      </span>
    </Link>
  </h1>
);

export default StandardHeader;
