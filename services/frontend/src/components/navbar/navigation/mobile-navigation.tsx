"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useMobileMenu } from "@/contexts/mobile-menu-context";
import MobileMenu from "../mobile-menu";
import { SidebarData } from "@/types";

type MobileNavigationProps = {
  data: SidebarData;
};

const MobileNavigation: React.FC<MobileNavigationProps> = ({ data }) => {
  const { toggleMenu, isMobileScreen } = useMobileMenu();

  return (
    <div className="md:hidden h-[40px] flex items-center justify-end">
      {/* Always render the button with the same dimensions */}
      <div className="w-[40px] h-[40px] flex items-center justify-center">
        <Button
          variant="ghost"
          className="p-2"
          onClick={toggleMenu}
          aria-label="Toggle mobile menu"
          disabled={!isMobileScreen}
          style={{ opacity: isMobileScreen ? 1 : 0 }}
        >
          <Menu className="text-secondary !w-7 !h-6" />
        </Button>
      </div>

      {isMobileScreen && <MobileMenu data={data} />}
    </div>
  );
};

export default MobileNavigation;
