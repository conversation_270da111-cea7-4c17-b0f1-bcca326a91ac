"use client";

import React from "react";
import { MainSearchInput } from "../main-search-input";
import { LanguageSwitcher } from "@/components/local-switcher";
// import Notifications from "@/components/navbar/notification";
import UserProfileButton from "@/components/navbar/user-profile-btn";

interface DesktopNavigationProps {
  searchPlaceholder: string;
  onSearch: (value: string) => void;
}

/**
 * Component for desktop navigation
 */
const DesktopNavigation: React.FC<DesktopNavigationProps> = ({
  searchPlaceholder,
  onSearch,
}) => (
  <div className="hidden md:flex items-center flex-1 gap-1 xl:gap-5">
    <div className="flex-1 flex justify-end">
      <MainSearchInput
        placeholder={searchPlaceholder}
        onSearch={onSearch}
        className="w-full max-w-[283px] min-w-[150px] hidden"
      />
    </div>

    <LanguageSwitcher />
    {/* <Notifications /> */}
    <UserProfileButton />
  </div>
);

export default DesktopNavigation;
