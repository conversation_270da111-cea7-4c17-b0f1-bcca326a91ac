import React from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import AutoSaveFormFieldRenderer from "./auto-save-form-field-renderer";
import { useAutoSaveSettings } from "@/hooks/use-auto-save-settings";
import { useTranslations } from "next-intl";
import { zodResolver } from "@hookform/resolvers/zod";
import { createSettingsValidationSchema } from "@/schemas/settings-schema";
import {
  getInputTypeFromAPI,
  convertValueForAPISubmission,
} from "@/utils/form-field-helpers";
import { SystemSetting } from "@/types/settings/system-settings";
import { TinputField } from "@/types";
import { getWeekendDayOptions } from "@/utils/date-utils";

type AutoSaveSettingsFormProps = {
  settings: SystemSetting[];
  onSettingUpdate?: () => void;
};

const AutoSaveSettingsForm: React.FC<AutoSaveSettingsFormProps> = ({
  settings,
  onSettingUpdate,
}) => {
  const { saveSetting } = useAutoSaveSettings();
  const t = useTranslations();

  const getTranslatedLabel = (
    key: string,
    namespace: string,
    fallback: string,
  ): string => {
    const translationKey = `common.settings.${namespace}.fields.${key}`;
    const label = t(translationKey);
    return label !== translationKey ? label : fallback;
  };

  const validationSchema = createSettingsValidationSchema(settings, t);

  const defaultValues = settings.reduce(
    (acc, { attributes }) => {
      const { key, value, setting_type } = attributes;

      // Handle array values for multi-select fields
      if (setting_type === "array") {
        if (Array.isArray(value)) {
          // Normalize weekend days values (remove leading zeros)
          if (key === "weekend_days") {
            acc[key] = value.map((day) => String(parseInt(day, 10)));
          } else {
            acc[key] = value;
          }
        } else {
          acc[key] = [];
        }
      } else {
        acc[key] = value;
      }

      return acc;
    },
    {} as Record<string, unknown>,
  );

  const form = useForm({
    resolver: zodResolver(validationSchema),
    defaultValues,
    mode: "onChange",
  });

  const handleSave = async (namespace: string, key: string, value: unknown) => {
    const setting = settings.find((s) => s.attributes.key === key);
    if (!setting) return;

    const { setting_type } = setting.attributes;

    const convertedValue = convertValueForAPISubmission(value, setting_type);

    await saveSetting(namespace, key, convertedValue);
    onSettingUpdate?.();
  };

  const getFieldConfig = (
    setting: SystemSetting,
  ): TinputField<Record<string, unknown>> & {
    namespace: string;
    settingKey: string;
  } => {
    const { key, description, is_editable, namespace, setting_type } =
      setting.attributes;

    const inputType = getInputTypeFromAPI(setting_type);
    const label = getTranslatedLabel(key, namespace, description);

    // Add options for specific array fields
    let options: Array<{ value: string; label: string }> | undefined;
    if (setting_type === "array" && key === "weekend_days") {
      options = getWeekendDayOptions(t);
    }

    return {
      name: key,
      type: inputType,
      label,
      placeholder:
        inputType === "switch" ? undefined : `Enter ${label.toLowerCase()}`,
      readOnly: !is_editable,
      namespace,
      settingKey: key,
      options,
    };
  };

  return (
    <Form {...form}>
      <div className="space-y-6">
        {settings.map((setting) => (
          <AutoSaveFormFieldRenderer
            key={setting.id}
            fieldConfig={getFieldConfig(setting)}
            form={form}
            onSave={handleSave}
            debounceMs={1000}
            showSaveStatus={true}
          />
        ))}
      </div>
    </Form>
  );
};

export default AutoSaveSettingsForm;
