import React, { useEffect, useRef, useMemo, useState } from "react";
import { FieldValues, Path, UseFormReturn } from "react-hook-form";
import { TinputField, TChangePasswordFormProps } from "@/types";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { useToastMessage } from "@/hooks/use-toast-message";
import { Check, Loader2 } from "lucide-react";
import debounce from "lodash/debounce";
import { useTranslations } from "next-intl";

type AutoSaveFormFieldRendererProps<T extends FieldValues> = {
  fieldConfig: TinputField<T> & {
    namespace?: string;
    settingKey?: string;
  };
  form: UseFormReturn<T>;
  onSave?: (namespace: string, key: string, value: any) => Promise<void>;
  debounceMs?: number;
  showSaveStatus?: boolean;
  isPending?: boolean;
  selectOpenState?: { [key: string]: boolean };
  onSelectOpenChange?: (name: string, open: boolean) => void;
  datePickerOpenState?: { [key: string]: boolean };
  onDatePickerOpenChange?: (name: string, open: boolean) => void;
  textInputWrapper?: string;
} & TChangePasswordFormProps;

type SaveStatus = "idle" | "saving" | "saved" | "error";

const AutoSaveFormFieldRenderer = <T extends FieldValues>({
  fieldConfig,
  form,
  onSave,
  debounceMs = 1000,
  showSaveStatus = true,
  ...props
}: AutoSaveFormFieldRendererProps<T>) => {
  const [saveStatus, setSaveStatus] = useState<SaveStatus>("idle");
  const t = useTranslations();
  const { showToast } = useToastMessage();
  const previousValueRef = useRef<any>(undefined);
  const lastFailedValueRef = useRef<any>(null);

  const fieldName = fieldConfig.name as Path<T>;
  const fieldValue = form.watch(fieldName);

  const debouncedSave = useMemo(
    () =>
      debounce(async (value: any) => {
        if (!onSave || !fieldConfig.namespace || !fieldConfig.settingKey)
          return;

        // Convert values to strings for comparison to avoid type issues
        const currentValue = String(value);
        const previousValue = String(previousValueRef.current);
        const lastFailedValue = String(lastFailedValueRef.current);

        // Prevent retrying same failing value or unchanged values
        if (
          currentValue === previousValue ||
          currentValue === lastFailedValue
        ) {
          return;
        }

        try {
          setSaveStatus("saving");

          await onSave(fieldConfig.namespace, fieldConfig.settingKey, value);
          setSaveStatus("saved");
          previousValueRef.current = value;
          lastFailedValueRef.current = null;
          setTimeout(() => setSaveStatus("idle"), 2000);
        } catch (error) {
          setSaveStatus("error");
          lastFailedValueRef.current = value;
          showToast(
            "error",
            t("common.toast.error") || "Failed to save setting",
          );
          setTimeout(() => setSaveStatus("idle"), 3000);
        }
      }, debounceMs),
    [debounceMs, onSave, fieldConfig.namespace, fieldConfig.settingKey, t],
  );

  useEffect(() => {
    // Initialize previousValueRef with the initial value if not set
    if (previousValueRef.current === undefined) {
      previousValueRef.current = fieldValue;
      return;
    }

    // Only trigger save if value actually changed and is not undefined/null
    if (
      fieldValue !== undefined &&
      fieldValue !== null &&
      String(fieldValue) !== String(previousValueRef.current) &&
      String(fieldValue) !== String(lastFailedValueRef.current) &&
      !form.getFieldState(fieldName).invalid
    ) {
      debouncedSave(fieldValue);
    }

    return () => {
      debouncedSave.cancel();
    };
  }, [fieldValue, debouncedSave]);

  const renderSaveStatus = () => {
    if (!showSaveStatus) return null;

    switch (saveStatus) {
      case "saving":
        return (
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
            <Loader2 className="w-3 h-3 animate-spin" />
            <span>{t("common.buttonText.saving")}</span>
          </div>
        );
      case "saved":
        return (
          <div className="flex items-center gap-1 text-xs text-secondary mt-1">
            <Check className="w-3 h-3" />
            <span>{t("common.buttonText.saved")}</span>
          </div>
        );
      case "error":
        return (
          <div className="flex items-center gap-1 text-xs text-red-600 mt-1">
            <span>{t("common.toast.error")}</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-1">
      <FormFieldRenderer fieldConfig={fieldConfig} form={form} {...props} />
      {renderSaveStatus()}
    </div>
  );
};

export default AutoSaveFormFieldRenderer;
