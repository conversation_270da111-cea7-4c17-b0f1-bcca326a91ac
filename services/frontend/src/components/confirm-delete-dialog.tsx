"use client";

import { Button } from "@/components/ui/button";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { FilledTrash } from "../../public/images/icons";

export type ItemDetail = {
  label: string;
  value: string;
  className?: string;
};

type ConfirmDeleteDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirmDelete: () => Promise<void>;
  isDeleting?: boolean;
  itemName: string;
  titleKey?: string;
  descriptionKey?: string;
  itemDetails?: ItemDetail[];
};

export default function ConfirmDeleteDialog({
  isOpen,
  onClose,
  onConfirmDelete,
  isDeleting = false,
  itemName,
  titleKey = "common.delete-dialog.title",
  descriptionKey = "common.delete-dialog.description",
  itemDetails = [],
}: ConfirmDeleteDialogProps) {
  const t = useTranslations();

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      className="text-center"
      closeBtnStyle="start-6 top-4 border-none"
      header={
        <>
          <div className="mx-auto mt-10 w-20 h-20 bg-red-50 rounded-full flex items-center justify-center">
            <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center">
              <FilledTrash className="scale-x-90 text-red-600" />
            </div>
          </div>

          <DialogTitle className="text-xl font-semibold text-gray-900 mb-2">
            {t(titleKey)}
          </DialogTitle>

          <DialogDescription className="text-gray-600 text-base font-normal leading-relaxed">
            {t(descriptionKey, { itemName: itemName })}
          </DialogDescription>
        </>
      }
    >
      {/* Item Details Section */}
      {itemDetails.length > 0 && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            {t("common.delete-dialog.item-details")}
          </h4>
          <div className="space-y-2">
            {itemDetails.map((detail, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{detail.label}:</span>
                <span
                  className={`text-sm font-medium ${detail.className || "text-gray-900"}`}
                >
                  {detail.value}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-3">
        <Button
          variant="ghost"
          onClick={onConfirmDelete}
          disabled={isDeleting}
          className="bg-red-50 text-error flex-1 h-12"
        >
          {isDeleting
            ? t("common.buttonText.deleting")
            : t("common.buttonText.delete")}
        </Button>
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isDeleting}
          className="bg-gray-100 flex-1 h-12"
        >
          {t("common.buttonText.cancel")}
        </Button>
      </div>
    </ResponsiveDialog>
  );
}
