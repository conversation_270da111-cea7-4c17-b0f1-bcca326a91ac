"use client";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import React from "react";

export interface IndicatorStep {
  id: string;
  label: string;
  status?: "pending" | "current" | "completed";
}

export interface IndicatorProps {
  steps: IndicatorStep[];
  currentStep?: string;
  orientation?: "horizontal" | "vertical";
  className?: string;
  onStepClick?: (stepId: string) => void;
}

export function Indicator({
  steps,
  currentStep,
  orientation = "horizontal",
  className,
  onStepClick,
}: IndicatorProps) {
  const getCurrentStepIndex = () => {
    if (!currentStep) return -1;
    return steps.findIndex((step) => step.id === currentStep);
  };

  const getStepStatus = (stepIndex: number): IndicatorStep["status"] => {
    const step = steps[stepIndex];
    if (step.status) return step.status;

    const currentIndex = getCurrentStepIndex();
    if (currentIndex === -1) return "pending";

    if (stepIndex < currentIndex) return "completed";
    if (stepIndex === currentIndex) return "current";
    return "pending";
  };

  if (orientation === "vertical") {
    return (
      <div className={cn("flex flex-col", className)}>
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isClickable = onStepClick && status !== "pending";
          const showConnector = index < steps.length - 1;

          return (
            <div key={step.id} className="flex items-start">
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-6 h-6 rounded-full border flex items-center justify-center transition-all duration-200",
                    status === "completed"
                      ? "bg-secondary border-secondary"
                      : status === "current"
                        ? "bg-primary border-primary"
                        : "bg-white border-slate-300",
                    isClickable && "cursor-pointer hover:scale-105",
                  )}
                  onClick={isClickable ? () => onStepClick(step.id) : undefined}
                >
                  {status === "completed" ? (
                    <Check className="w-3 h-3 text-white" />
                  ) : (
                    <div
                      className={cn(
                        "w-2 h-2 rounded-full",
                        status === "current" ? "bg-white" : "bg-slate-400",
                      )}
                    />
                  )}
                </div>
                {showConnector && (
                  <div
                    className={cn(
                      "w-0.5 h-12 mt-2",
                      status === "completed" ? "bg-gray-200" : "bg-slate-300",
                    )}
                  />
                )}
              </div>
              <div className="ml-4 pb-8">
                <span
                  className={cn(
                    "text-sm font-medium",
                    status === "current" ? "text-slate-900" : "text-slate-500",
                  )}
                >
                  {step.label}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  // Horizontal orientation (matches the image exactly)
  return (
    <div
      className={cn("flex flex-col items-center w-full", className)}
      dir="rtl"
    >
      {/* Step Circles + Connectors */}
      <div className="flex items-center justify-center w-full">
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isClickable = onStepClick && status !== "pending";

          return (
            <React.Fragment key={step.id}>
              {/* Circle */}
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-6 h-6 rounded-full border flex items-center justify-center transition-all duration-200",
                    status === "completed"
                      ? "bg-secondary border-secondary"
                      : status === "current"
                        ? "bg-white border-[1px] border-black"
                        : "bg-white border-slate-300",
                    isClickable && "cursor-pointer hover:scale-105",
                  )}
                  onClick={isClickable ? () => onStepClick(step.id) : undefined}
                >
                  {status === "completed" ? (
                    <Check className="w-3 h-3 text-white" />
                  ) : (
                    <div
                      className={cn(
                        "w-2.5 h-2.5 rounded-full",
                        status === "current" ? "bg-secondary" : "bg-gray-300",
                      )}
                    />
                  )}
                </div>
              </div>

              {/* Connector */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "h-0.5 w-20 mx-2 rounded-full", // adjust w-12 as needed
                    status === "completed" ? "bg-gray-200" : "bg-slate-200",
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Labels aligned under each circle */}
      <div className="flex justify-center w-full mt-2 gap-12">
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          return (
            <div key={step.id} className="text-center">
              <span
                className={cn(
                  "text-xs font-medium leading-5",
                  status === "current"
                    ? "text-secondary"
                    : status === "completed"
                      ? "text-black"
                      : "text-gray-500",
                )}
              >
                {step.label}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
