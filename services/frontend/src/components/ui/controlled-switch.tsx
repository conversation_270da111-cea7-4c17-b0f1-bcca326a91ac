"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";

interface ControlledSwitchProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  description?: string;
  labelPosition?: "left" | "right";
  dir?: "ltr" | "rtl";
}

const ControlledSwitch: React.FC<ControlledSwitchProps> = ({
  checked,
  onCheckedChange,
  disabled = false,
  className,
  label,
  description,
  labelPosition = "left",
  dir,
}) => {
  const locale: Locale = useLocale() as Locale;
  const isRTL = locale === "ar";
  const switchDir = dir || (isRTL ? "rtl" : "ltr");

  const switchElement = (
    <Switch
      dir="ltr" // Switch component itself should always be LTR for consistent behavior
      checked={checked}
      onCheckedChange={onCheckedChange}
      disabled={disabled}
      className={className}
    />
  );

  const labelElement = label && (
    <div className="flex flex-col">
      <span className="capitalize text-gray-500 font-medium text-sm leading-5">
        {label}
      </span>
      {description && (
        <span className="text-xs text-gray-500">{description}</span>
      )}
    </div>
  );

  if (!label) {
    return switchElement;
  }

  const shouldShowLabelFirst =
    (labelPosition === "left" && !isRTL) ||
    (labelPosition === "right" && isRTL);

  return (
    <div
      className={cn(
        "flex items-center justify-between gap-4",
        switchDir === "rtl" ? "flex-row-reverse" : "flex-row",
      )}
      dir={switchDir}
    >
      {shouldShowLabelFirst ? (
        <>
          {labelElement}
          {switchElement}
        </>
      ) : (
        <>
          {switchElement}
          {labelElement}
        </>
      )}
    </div>
  );
};

export { ControlledSwitch };
