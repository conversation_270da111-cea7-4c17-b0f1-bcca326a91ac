"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface TagProps extends React.HTMLAttributes<HTMLDivElement> {
  bordered?: boolean;
  style?: React.CSSProperties;
}

const Tag = React.forwardRef<HTMLDivElement, TagProps>(
  ({ className, bordered = false, children, style, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm",
          bordered && "border",
          className,
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  },
);
Tag.displayName = "Tag";

export { Tag };
