"use client";

import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Input } from "./input";

type IPAddressInputProps = {
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
};

export const IPAddressInput: React.FC<IPAddressInputProps> = ({
  value = "",
  onChange,
  onBlur,
  disabled = false,
  className,
  placeholder = "*************",
}) => {
  const [ipParts, setIpParts] = useState<string[]>(() => {
    if (value) {
      const parts = value.split(".");
      return [...parts, ...Array(4 - parts.length).fill("")];
    }
    return ["", "", "", ""];
  });

  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (value) {
      const parts = value.split(".");
      setIpParts([...parts, ...Array(4 - parts.length).fill("")]);
    } else {
      // Reset to empty when value is empty
      setIpParts(["", "", "", ""]);
    }
  }, [value]);

  const handleInputChange = (index: number, inputValue: string) => {
    // Only allow numbers and limit to 3 digits
    const numericValue = inputValue.replace(/[^0-9]/g, "").slice(0, 3);

    // Validate range (0-255)
    const numValue = parseInt(numericValue);
    if (numericValue !== "" && (numValue < 0 || numValue > 255)) {
      return;
    }

    const newParts = [...ipParts];
    newParts[index] = numericValue;
    setIpParts(newParts);

    // Create the full IP string
    const fullIP = newParts.join(".");
    onChange?.(fullIP);

    // Auto-focus next input if current is complete (3 digits or value >= 100)
    if (
      numericValue.length === 3 ||
      (numericValue.length === 2 && numValue >= 25) ||
      (numericValue.length === 1 && numValue >= 3)
    ) {
      if (index < 3 && inputRefs.current[index + 1]) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle backspace to move to previous input
    if (e.key === "Backspace" && ipParts[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    // Handle arrow keys
    if (e.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    if (e.key === "ArrowRight" && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Handle dot to move to next input
    if (e.key === "." && index < 3) {
      e.preventDefault();
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const match = pastedText.match(ipRegex);

    if (match) {
      const parts = match.slice(1, 5);
      // Validate each part is within range
      const validParts = parts.every((part) => {
        const num = parseInt(part);
        return num >= 0 && num <= 255;
      });

      if (validParts) {
        setIpParts(parts);
        onChange?.(pastedText);
      }
    }
  };

  return (
    <div dir="ltr" className={cn("flex items-center gap-2", className)}>
      {ipParts.map((part, index) => (
        <React.Fragment key={index}>
          <Input
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            type="text"
            value={part}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={index === 0 ? handlePaste : undefined}
            onBlur={onBlur}
            disabled={disabled}
            className={cn(
              "w-full h-12 text-center border border-gray-300 rounded-lg",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "disabled:bg-gray-100 disabled:cursor-not-allowed",
              "text-lg font-medium",
            )}
            placeholder={placeholder.split(".")[index] || ""}
            maxLength={3}
          />
          {index < 3 && (
            <span className="text-gray-400 text-lg font-medium">.</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
