"use client";

import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as SelectPrimitive from "@radix-ui/react-select";

interface ControlledSelectProps {
  onValueChange: (value: string) => void;
  defaultValue?: string;
  value?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  triggerClassName?: string;
  children?: React.ReactNode;
  options?: { value: string; label: string; disabled?: boolean }[];
  dir?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ControlledSelect({
  onValueChange,
  defaultValue,
  value,
  disabled,
  placeholder,
  className,
  triggerClassName,
  children,
  options,
  dir,
  open,
  onOpenChange,
}: ControlledSelectProps) {
  // Internal state for uncontrolled usage
  const [internalOpen, setInternalOpen] = React.useState(false);

  // Determine if we're in controlled or uncontrolled mode
  const isControlled = open !== undefined;
  const isOpen = isControlled ? open : internalOpen;

  // Handle open state changes
  const handleOpenChange = React.useCallback(
    (newOpen: boolean) => {
      if (!isControlled) {
        setInternalOpen(newOpen);
      }
      onOpenChange?.(newOpen);
    },
    [isControlled, onOpenChange],
  );

  return (
    <SelectPrimitive.Root
      open={isOpen}
      onOpenChange={handleOpenChange}
      onValueChange={onValueChange}
      defaultValue={defaultValue}
      value={value}
      disabled={disabled}
    >
      <SelectTrigger className={`${triggerClassName} shadow min-h-10`}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent dir={dir} className={className}>
        {options
          ? options.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                disabled={option.disabled ?? false}
              >
                {option.label}
              </SelectItem>
            ))
          : children}
      </SelectContent>
    </SelectPrimitive.Root>
  );
}
