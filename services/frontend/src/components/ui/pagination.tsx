import * as React from "react";
import {
  ChevronLeft,
  ChevronRight,
  Minus,
  MoreHorizontal,
  Plus,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { ButtonProps, buttonVariants } from "@/components/ui/button";
import Link from "next/link";

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
);
Pagination.displayName = "Pagination";

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
));
PaginationContent.displayName = "PaginationContent";

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
));
PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = {
  isActive?: boolean;
} & Pick<ButtonProps, "size"> &
  React.ComponentProps<typeof Link>;

const PaginationLink = ({
  className,
  isActive,
  size = "icon",
  ...props
}: PaginationLinkProps) => (
  <Link
    aria-current={isActive ? "page" : undefined}
    className={cn(
      buttonVariants({
        variant: "ghost",
        size,
      }),
      `${
        isActive
          ? "bg-background-v2 text-secondary"
          : "hover:bg-background-v2 text-secondary"
      }`,
      className,
    )}
    {...props}
  />
);
PaginationLink.displayName = "PaginationLink";

import { useTranslations } from "next-intl"; // Import useTranslations

const PaginationPrevious = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => {
  const t = useTranslations("common.pagination"); // Get translations

  return (
    <PaginationLink
      aria-label={t("previous")}
      size="default"
      className={cn("gap-1 pl-4 text-xs lg:text-sm font-medium", className)}
      {...props}
    >
      <div className="hidden md:block">
        <span className="me-1 flex items-center justify-between gap-2">
          <Minus /> <span className="ms-1">{t("previous")}</span>
        </span>
      </div>
      <ChevronRight className="!w-6 !h-6 ltr:rotate-180 md:hidden text-[#292D32] hover:text-red-900 stroke-1" />
    </PaginationLink>
  );
};
PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => {
  const t = useTranslations("common.pagination"); // Get translations

  return (
    <PaginationLink
      aria-label={t("next")}
      size="default"
      className={cn("gap-1 pl-4 text-xs lg:text-sm font-medium", className)}
      {...props}
    >
      <div className="hidden md:block">
        <span className="me-1 flex items-center justify-between gap-2">
          <Plus /> <span className="ms-1">{t("next")}</span>
        </span>
      </div>
      <ChevronLeft className="!w-6 !h-6 ltr:rotate-180 text-[#292D32] md:hidden hover:text-red-900 stroke-1" />
    </PaginationLink>
  );
};
PaginationNext.displayName = "PaginationNext";

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
);
PaginationEllipsis.displayName = "PaginationEllipsis";

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
};
