import { cn } from "@/lib/utils";
import React from "react";

type TheaderProps = {
  title: string;
  label: string;
};

const Header = ({ title, label }: TheaderProps) => {
  return (
    <header className="flex justify-center items-start flex-col text-start w-full gap-1">
      <h1 className={cn("font-bold text-auth-primary text-[16px]")}>{title}</h1>
      <p className="text-sm font-normal text-auth-secondary">{label}</p>
    </header>
  );
};

export default Header;
