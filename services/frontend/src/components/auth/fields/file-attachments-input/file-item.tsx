"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { Download, PDF } from "../../../../../public/images/icons";
import { formatFileSize } from "@/lib/formateFileSize";
import { downloadFile } from "@/lib/downloadFile";
import { File } from "lucide-react";
import { useTranslations } from "next-intl";

type FileItemProps = {
  file: File;
  index: number;
  onRemove: (index: number) => void;
  isPending?: boolean;
  readOnly?: boolean;
};

export default function FileItem({
  file,
  index,
  onRemove,
  isPending,
  readOnly,
}: FileItemProps) {
  const t = useTranslations();

  // Handle file download (for local files, create object URL)
  const handleDownload = () => {
    const url = URL.createObjectURL(file);
    downloadFile(url, file.name);
    // Revoke the URL to free up memory
    URL.revokeObjectURL(url);
  };

  // Get file icon based on file type
  const getFileIcon = () => {
    // You can expand this to show different icons for different file types
    return file.type === "application/pdf" ? (
      <PDF className="!h-7 !w-7" />
    ) : (
      <File className="text-gray-500 w-7 h-7" />
    );
  };

  return (
    <div className="flex items-center justify-between bg-gray-100 p-3 rounded-lg">
      <div className="flex items-center gap-2.5">
        <div>{getFileIcon()}</div>
        <div className="flex flex-col items-start gap-0.5">
          <span className="font-medium text-secondary text-sm max-w-[350px] break-words">
            {file.name}
          </span>
          <span className="text-gray-500 text-xs">
            {formatFileSize(file.size, t)}
          </span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button
          type="button"
          onClick={handleDownload}
          variant="ghost"
          size="sm"
          title={t("common.buttonText.download")}
        >
          <Download className="!w-5 !h-5" />
        </Button>
        <Button
          type="button"
          onClick={() => onRemove(index)}
          variant="ghost"
          size="sm"
          className="p-1 h-8 w-8 text-error hover:text-error"
          title={t("common.buttonText.delete")}
          disabled={isPending || readOnly}
        >
          <Trash2 className="!w-5 !h-5" />
        </Button>
      </div>
    </div>
  );
}
