"use client";

import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { LANGUAGES } from "@/constants/enum";
import { Button } from "@/components/ui/button";
import { getMonthTranslations } from "@/constants/translations-mapping";
import { useTranslations } from "next-intl";

const weekDays = {
  en: ["Sat", "Sun", "Mon", "Tue", "Wed", "Thu", "Fri"],
  ar: ["سبت", "أحد", "إثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة"],
};

const monthNames = {
  1: "يناير",
  2: "فبراير",
  3: "مارس",
  4: "أبريل",
  5: "مايو",
  6: "يونيو",
  7: "يوليو",
  8: "أغسطس",
  9: "سبتمبر",
  10: "أكتوبر",
  11: "نوفمبر",
  12: "ديسمبر",
};

export type DateType = {
  day: number;
  month: number;
  year: number;
};

type MultiDateSelectorProps = {
  lang: LANGUAGES.ENGLISH | LANGUAGES.ARABIC;
  initialDates?: DateType[];
  onChange: (dates: DateType[]) => void;
};

export function MultiDateSelector({
  lang,
  initialDates = [],
  onChange,
}: MultiDateSelectorProps) {
  // direction based on lang
  const isRtl = lang === LANGUAGES.ARABIC;
  const t = useTranslations();

  // State for selected dates
  const [selectedDates, setSelectedDates] = useState<DateType[]>(initialDates);

  // Reference date controls the week shown
  // Default: today or first selected date
  const [referenceDate, setReferenceDate] = useState<Date>(
    initialDates.length > 0
      ? new Date(
          initialDates[0].year,
          initialDates[0].month - 1,
          initialDates[0].day,
        )
      : new Date(),
  );

  // Helper: check if date is selected
  const isDateSelected = (day: number, month: number, year: number) =>
    selectedDates.some(
      (d) => d.day === day && d.month === month && d.year === year,
    );

  // Toggle a date in selection
  const toggleDate = (day: number, month: number, year: number) => {
    const exists = isDateSelected(day, month, year);
    let newSelected;

    if (exists) {
      newSelected = selectedDates.filter(
        (d) => !(d.day === day && d.month === month && d.year === year),
      );
    } else {
      newSelected = [...selectedDates, { day, month, year }];
    }
    setSelectedDates(newSelected);

    onChange(newSelected);
  };

  // Generate 7 days starting from referenceDate - 3 days (to center)
  const generateDates = () => {
    const dates = [];
    const start = new Date(referenceDate);
    start.setDate(referenceDate.getDate() - 3);

    for (let i = 0; i < 7; i++) {
      const date = new Date(start);
      date.setDate(start.getDate() + i);

      dates.push({
        day: date.getDate(),
        month: date.getMonth() + 1,
        year: date.getFullYear(),
        dayName: weekDays[lang][date.getDay()],
      });
    }
    return dates;
  };

  // Navigate 7 days forward or backward
  const navigate = (dir: "prev" | "next") => {
    const newRef = new Date(referenceDate);
    newRef.setDate(referenceDate.getDate() + (dir === "prev" ? -7 : 7));
    setReferenceDate(newRef);
  };

  const dates = generateDates();

  return (
    <div dir={isRtl ? "rtl" : "ltr"} className="relative select-none">
      {/* Days of week header */}
      <div className="grid grid-cols-7 text-center py-2 text-secondary bg-primary rounded-t-lg font-medium leading-5 text-sm">
        {weekDays[lang].map((dayName, i) => (
          <div key={i} className="px-1">
            {dayName}
          </div>
        ))}
      </div>

      {/* Dates row with navigation */}
      <div className="relative flex items-center bg-white rounded-b-lg shadow-sm">
        <Button
          type="button"
          onClick={() => navigate("prev")}
          className={cn(
            "absolute top-1/2 -translate-y-1/2 p-1 w-[14px] h-[14px] rounded-full bg-white text-gray-600 shadow-md hover:bg-white z-10",
            isRtl ? "right-0" : "left-0",
          )}
          aria-label="Previous 7 days"
        >
          <ChevronLeft className={cn("w-5 h-5", isRtl ? "rotate-180" : "")} />
        </Button>

        <div className="grid grid-cols-7 gap-1 w-full px-10 py-2">
          {dates.map(({ day, month, year }, i) => {
            const selected = isDateSelected(day, month, year);
            return (
              <Button
                variant={"ghost"}
                key={i}
                type="button"
                onClick={() => toggleDate(day, month, year)}
                className={cn(
                  "flex flex-col items-center h-[52px] w-12 gap-0.5 justify-center p-1.5 rounded-lg transition-colors font-normal text-sm",
                  selected ? "bg-green-900 text-white" : "hover:bg-gray-100",
                )}
              >
                <span className="text-lg font-medium">{day}</span>
                <span className="text-xs">
                  {getMonthTranslations(
                    monthNames[month as keyof typeof monthNames],
                    t,
                  )}
                </span>
              </Button>
            );
          })}
        </div>

        <Button
          variant={"ghost"}
          type="button"
          onClick={() => navigate("next")}
          className={cn(
            "absolute top-1/2 -translate-y-1/2 p-1 w-[14px] h-[14px] rounded-full bg-white hover:bg-white text-gray-600 shadow-md z-10",
            isRtl ? "left-0" : "right-0",
          )}
          aria-label="Next 7 days"
        >
          <ChevronRight className={cn("w-5 h-5", isRtl ? "rotate-180" : "")} />
        </Button>
      </div>
    </div>
  );
}
