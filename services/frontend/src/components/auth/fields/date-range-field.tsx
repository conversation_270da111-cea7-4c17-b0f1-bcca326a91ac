"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import { DateRangeModal } from "@/components/modals/date-range-modal";
import { formatDate } from "@/lib/dateFormatter";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { TFunction } from "@/types";

// Date range type for start and end dates
export type DateRangeType = {
  startDate: Date;
  endDate: Date;
};

interface DateRangeFieldProps {
  value?: DateRangeType;
  onChange: (dateRange: DateRangeType) => void;
  placeholder?: string;
  disabled?: boolean;
  // Customizable props for different use cases
  title?: string;
  description?: string;
  submitButtonText?: string;
  startDateLabel?: string;
  endDateLabel?: string;
  // Validation props
  allowPastDates?: boolean;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

export function DateRangeField({
  value,
  onChange,
  placeholder,
  disabled = false,
  title,
  description,
  submitButtonText,
  startDateLabel,
  endDateLabel,
  allowPastDates = true,
  minDate,
  maxDate,
  className,
}: DateRangeFieldProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const locale: Locale = useLocale() as Locale;
  const t = useTranslations() as TFunction;

  const handleDateUpdate = (startDate: Date, endDate: Date) => {
    const dateRange: DateRangeType = { startDate, endDate };
    onChange(dateRange);
    setIsModalOpen(false);
  };

  // Format display text
  const getDisplayText = () => {
    if (!value?.startDate || !value?.endDate) {
      return placeholder || t("common.form.date-range.placeholder");
    }

    if (value.startDate.getTime() === value.endDate.getTime()) {
      // Single date
      return formatDate(value.startDate, locale);
    } else {
      // Date range
      return `${formatDate(value.startDate, locale)} - ${formatDate(
        value.endDate,
        locale,
      )}`;
    }
  };

  return (
    <>
      <Button
        type="button"
        variant="outline"
        className={`w-full justify-start text-left font-normal h-10 px-3 py-2 ${className}`}
        onClick={() => !disabled && setIsModalOpen(true)}
        disabled={disabled}
      >
        <Calendar className="mr-2 h-4 w-4" />
        <span className={!value?.startDate ? "text-muted-foreground" : ""}>
          {getDisplayText()}
        </span>
      </Button>

      <DateRangeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleDateUpdate}
        initialStartDate={value?.startDate}
        initialEndDate={value?.endDate}
        isLoading={false}
        title={title}
        description={description}
        confirmButtonText={submitButtonText}
        startDateLabel={startDateLabel}
        endDateLabel={endDateLabel}
        allowPastDates={allowPastDates}
        minDate={minDate}
        maxDate={maxDate}
      />
    </>
  );
}
