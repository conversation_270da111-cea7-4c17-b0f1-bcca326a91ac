"use client";

import React from "react";
import {
  ControllerRenderProps,
  FieldValues,
  Path,
  useFormContext,
} from "react-hook-form";
import { cn } from "@/lib/utils";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import ar from "react-phone-input-2/lang/ar.json";
import "./phone-input-styles.css";

type PhoneInputProps<T extends FieldValues> = {
  field: ControllerRenderProps<T, Path<T>>;
  placeholder?: string;
  className?: string;
  isPending?: boolean;
  readOnly?: boolean;
};

export default function CustomPhoneInput<T extends FieldValues>({
  field,
  placeholder,
  className,
  isPending,
  readOnly,
}: PhoneInputProps<T>) {
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;
  const form = useFormContext();

  return (
    <div className={cn("relative", className)}>
      <PhoneInput
        localization={isAr ? ar : undefined}
        excludeCountries={["il"]}
        country={"jo"}
        preferredCountries={["jo", "eg"]}
        value={field.value}
        onChange={field.onChange}
        onBlur={() => {
          field.onBlur();

          if (form && field.name) {
            form.trigger(field.name);
          }
        }}
        inputProps={{
          name: field.name,
          placeholder: placeholder || "Phone Number",
          disabled: isPending || readOnly,
          readOnly: readOnly,
          className: cn(
            "phone-input-style",
            isAr ? "ps-3 pe-14" : "ps-14 pe-3",
            {
              "opacity-50 cursor-not-allowed": isPending || readOnly,
              "border-red-500 focus-visible:ring-red-500":
                form && field.name && form.formState.errors[field.name],
            },
            className,
          ),
        }}
        containerClass={cn("w-full relative", {
          "opacity-50 cursor-not-allowed": isPending || readOnly,
        })}
        buttonClass={cn(
          "border-none  absolute top-0 bottom-0 z-10",
          isAr ? "right-0 rounded-r-md" : "left-0 rounded-l-md",
          {
            "opacity-50 cursor-not-allowed": isPending || readOnly,
          },
        )}
        dropdownClass={cn(
          "country-list-style",
          isAr ? "text-right" : "text-left",
        )}
        searchClass="search-class-style"
        enableSearch={true}
        disableSearchIcon={true}
        searchPlaceholder={isAr ? "ابحث عن البلد..." : "Search country..."}
        searchStyle={{
          width: "100%",
          padding: "4px 10px",
          margin: "0",
          border: "1px solid var(--input)",
          fontSize: "0.9rem",
        }}
      />
    </div>
  );
}
