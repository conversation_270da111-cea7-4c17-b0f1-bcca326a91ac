import { getTranslations } from "next-intl/server";
import Navbar from "../navbar";
import Sidebar from "./index";
import { TSystems, TFunction } from "@/types";
import { getSidebarData } from "@/constants";
import Footer from "../footer";
import { SettingsModalProvider } from "@/contexts/settings-modal-context";
import { SystemProvider } from "@/contexts/system-provider";
import { Suspense } from "react";
import SWRProvider from "@/providers/swr-provider";
import { PermissionProvider } from "@/contexts/PermissionContext";

type SystemLayoutProps = {
  children: React.ReactNode;
  system: TSystems;
};

export default async function SystemLayout({
  children,
  system,
}: SystemLayoutProps) {
  const t = (await getTranslations()) as TFunction;
  const sidbarData = getSidebarData(t);

  return (
    <div className="flex min-h-full">
      <SystemProvider currSystem={system}>
        <PermissionProvider>
          <SettingsModalProvider>
            <Sidebar data={sidbarData[system]!} />
            <div className="flex-1 w-full md:py-2.5 lg:py-[22px] md:my-3 bg-white md:bg-[#FBFBFC] md:rounded-e-none md:rounded-3xl">
              <Suspense
                fallback={
                  <div className="flex min-h-[42px] items-center container max-md:px-2 max-md:py-3 md:mb-5 justify-between max-md:border-b">
                    Loading content...
                  </div>
                }
              >
                <Navbar data={sidbarData[system]!} />
              </Suspense>
              <main className="pb-4 max-md:px-4 container min-h-[calc(100vh-170px)] md:max-h-[calc(100vh-170px)] md:overflow-auto custom-scroll flex flex-col">
                <SWRProvider>{children}</SWRProvider>
              </main>
              <Footer />
            </div>
          </SettingsModalProvider>
        </PermissionProvider>
      </SystemProvider>
    </div>
  );
}
