"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ar, enUS } from "date-fns/locale";
import { Locale } from "@/i18n/routing";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { formatDate } from "@/lib/dateFormatter";
import CustomTimePicker from "@/components/custom-time-picker";
import { createDateWithoutTimezoneIssue } from "@/lib/utils";
import { convertTo12Hour } from "@/lib/time-format";

type SingleDateTimeModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (dateTime: Date) => void;
  initialDateTime?: string | Date;
  isLoading?: boolean;
  // Customizable content
  title?: string;
  description?: string;
  // Validation options
  allowPastDates?: boolean;
  minDate?: Date;
  maxDate?: Date;
  mode?: "create" | "update";
};

export function SingleDateTimeModal({
  isOpen,
  onClose,
  onConfirm,
  initialDateTime,
  isLoading = false,
  title,
  description,
  allowPastDates = false,
  minDate,
  maxDate,
  mode = "create",
}: SingleDateTimeModalProps) {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  // Convert initial date to Date object, similar to DateRangeModal
  const getInitialDate = (): Date => {
    if (!initialDateTime) return new Date();
    if (typeof initialDateTime === "string") {
      return createDateWithoutTimezoneIssue(initialDateTime) || new Date();
    }
    return initialDateTime;
  };

  const initialDate = getInitialDate();

  // Helper function to format time consistently for CustomTimePicker
  const formatTimeForPicker = (date: Date): string => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const timeString = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
    const { hour, minute, period } = convertTo12Hour(timeString, locale);

    return `${hour}:${minute} ${period}`;
  };

  // State for selected date and time
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);
  const [selectedTime, setSelectedTime] = useState<string>(
    formatTimeForPicker(initialDate),
  );

  const handleConfirm = () => {
    try {
      // CustomTimePicker returns 24-hour format (like "14:30")
      // Browser toLocaleTimeString returns 12-hour format (like "2:30 PM" or "2:30 م")

      if (!selectedTime.includes(" ")) {
        // 24-hour format from CustomTimePicker
        const [hours, minutes] = selectedTime.split(":");
        const hour24 = parseInt(hours);
        const minute24 = parseInt(minutes);

        if (
          isNaN(hour24) ||
          isNaN(minute24) ||
          hour24 < 0 ||
          hour24 > 23 ||
          minute24 < 0 ||
          minute24 > 59
        ) {
          throw new Error("Invalid time values");
        }

        const combinedDateTime = new Date(selectedDate);
        combinedDateTime.setHours(hour24, minute24, 0, 0);
        onConfirm(combinedDateTime);
      } else {
        // 12-hour format from browser
        const [time, period] = selectedTime.split(" ");
        const [hours, minutes] = time.split(":");
        let hour24 = parseInt(hours);
        const minute24 = parseInt(minutes);

        if (
          isNaN(hour24) ||
          isNaN(minute24) ||
          hour24 < 1 ||
          hour24 > 12 ||
          minute24 < 0 ||
          minute24 > 59
        ) {
          throw new Error("Invalid time values");
        }

        // Convert to 24-hour format
        if (period === "PM" || period === "م") {
          if (hour24 !== 12) hour24 += 12;
        } else if (period === "AM" || period === "ص") {
          if (hour24 === 12) hour24 = 0;
        }

        const combinedDateTime = new Date(selectedDate);
        combinedDateTime.setHours(hour24, minute24, 0, 0);
        onConfirm(combinedDateTime);
      }

      onClose();
    } catch (error) {
      console.error("Error parsing time:", error);
      // Fallback to current time if parsing fails
      const fallbackDateTime = new Date(selectedDate);
      onConfirm(fallbackDateTime);
      onClose();
    }
  };

  const handleCancel = () => {
    // Reset to initial values
    setSelectedDate(initialDate);
    setSelectedTime(
      initialDate.toLocaleTimeString(locale, {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    );
    onClose();
  };

  // Date validation function
  const isDateDisabled = (date: Date) => {
    if (!allowPastDates && date < new Date(new Date().setHours(0, 0, 0, 0))) {
      return true;
    }
    if (minDate && date < minDate) {
      return true;
    }
    if (maxDate && date > maxDate) {
      return true;
    }
    return false;
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      className="rounded-2xl"
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {title || t("common.modals.datetime.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {description || t("common.modals.datetime.description")}
            </DialogDescription>
          </div>
        </>
      }
    >
      <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
        <div className="flex-1 flex flex-col gap-6 min-h-[calc(100vh-15rem)] pb-6">
          {/* Date Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("common.modals.datetime.date")}
            </label>
            <div className="border rounded-md p-4 bg-white w-full">
              <Calendar
                locale={isAr ? ar : enUS}
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                disabled={isDateDisabled}
                showOutsideDays={false}
                classNames={{
                  root: "w-full",
                  month: "space-y-4 w-full",
                  caption: "flex justify-center pt-1 relative items-center",
                  caption_label: "text-sm font-medium",
                  nav: "space-x-1 flex items-center",
                  nav_button:
                    "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                  nav_button_previous: "absolute left-1",
                  nav_button_next: "absolute right-1",
                  table: "w-full border-collapse space-y-1",
                  head_row: "flex w-full justify-between",
                  head_cell:
                    "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] text-center",
                  row: "flex w-full mt-2 justify-between",
                  cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                  day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
                  day_selected:
                    "bg-secondary text-primary-foreground hover:bg-primary rounded-sm hover:text-primary-foreground focus:bg-secondary focus:text-primary-foreground",
                  day_today: "bg-gray-200 rounded-sm text-accent-foreground",
                  day_outside: "text-muted-foreground opacity-50",
                  day_disabled: "text-muted-foreground opacity-50",
                  day_hidden: "invisible",
                }}
              />
            </div>
          </div>

          {/* Time Selection */}
          <div className="space-y-2 flex gap-2 items-center">
            <label className="text-sm font-medium text-gray-700">
              {t("common.modals.datetime.time")}
            </label>
            <div className="flex justify-start">
              <CustomTimePicker
                value={selectedTime}
                onChange={setSelectedTime}
                locale={locale}
                maxWidth="200px"
              />
            </div>
          </div>

          {/* Selected DateTime Display */}
          <div className="p-3 bg-gray-50 rounded-md">
            <div className="text-sm font-medium text-gray-700 mb-1">
              {t("common.modals.datetime.selected")}
            </div>
            <div className="text-sm text-gray-600">
              {formatDate(selectedDate, locale, "dd-MM-yyyy")} {selectedTime}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="sticky border-t border-t-slate-200 pt-6 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 px-6">
          <Button
            type="button"
            onClick={handleConfirm}
            disabled={isLoading}
            className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
          >
            {isLoading && (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            )}
            {t("common.confirm")}
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full h-12 sm:max-w-[244px] rounded-lg"
            onClick={handleCancel}
            disabled={isLoading}
          >
            {t("common.cancel")}
          </Button>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
