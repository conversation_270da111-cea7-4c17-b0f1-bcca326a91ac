"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { DateRange } from "react-day-picker";
import { ar, enUS } from "date-fns/locale";
import { Locale } from "@/i18n/routing";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { formatDate } from "@/lib/dateFormatter";
import { createDateWithoutTimezoneIssue } from "@/lib/utils";

type DateRangeModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (startDate: Date, endDate: Date) => void;
  initialStartDate?: string | Date;
  initialEndDate?: string | Date;
  isLoading?: boolean;
  // Customizable content
  title?: string;
  description?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  startDateLabel?: string;
  endDateLabel?: string;
  selectedRangeLabel?: string;
  noneLabel?: string;
  // Validation options
  allowPastDates?: boolean;
  minDate?: Date;
  maxDate?: Date;
  mode?: "create" | "update";
};

export function DateRangeModal({
  isOpen,
  onClose,
  onConfirm,
  initialStartDate,
  initialEndDate,
  isLoading = false,
  title,
  description,
  confirmButtonText,
  cancelButtonText,
  startDateLabel,
  endDateLabel,
  selectedRangeLabel,
  noneLabel,
  allowPastDates = true,
  minDate,
  maxDate,
  mode = "create",
}: DateRangeModalProps) {
  const initialStartDateObj = createDateWithoutTimezoneIssue(initialStartDate);
  const initialEndDateObj = createDateWithoutTimezoneIssue(initialEndDate);

  // State for selected date range
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: initialStartDateObj,
    to: initialEndDateObj,
  });

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  // Validation errors
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = () => {
    // Reset error
    setError(null);

    // Validate dates
    if (!dateRange?.from || !dateRange?.to) {
      setError(
        t("common.form.date-range.errors.dates-required") ||
          "Please select both start and end dates",
      );
      return;
    }

    // Validate past dates if not allowed
    if (!allowPastDates) {
      const today = new Date(new Date().setHours(0, 0, 0, 0));
      if (dateRange.from < today) {
        setError(
          t("common.form.date-range.errors.past-date") ||
            "Start date cannot be in the past",
        );
        return;
      }
    }

    // Validate min/max dates
    if (minDate && dateRange.from < minDate) {
      setError(
        t("common.form.date-range.errors.min-date") ||
          "Start date is too early",
      );
      return;
    }

    if (maxDate && dateRange.to > maxDate) {
      setError(
        t("common.form.date-range.errors.max-date") || "End date is too late",
      );
      return;
    }

    // Call the confirm function
    onConfirm(dateRange.from, dateRange.to);
  };

  // Get disabled dates function
  const getDisabledDates = (date: Date) => {
    if (!allowPastDates && date < new Date(new Date().setHours(0, 0, 0, 0))) {
      return true;
    }
    if (minDate && date < minDate) {
      return true;
    }
    if (maxDate && date > maxDate) {
      return true;
    }
    return false;
  };

  // Default labels
  const modalTitle =
    title || t("common.form.date-range.modal.title") || "Select Date Range";
  const modalDescription =
    description ||
    t("common.form.date-range.modal.description") ||
    "Choose start and end dates";
  const confirmText =
    confirmButtonText || t("common.form.date-range.modal.confirm") || "Confirm";
  const cancelText =
    cancelButtonText || t("common.form.date-range.modal.cancel") || "Cancel";
  const startLabel =
    startDateLabel || t("common.form.date-range.start-date") || "Start Date";
  const endLabel =
    endDateLabel || t("common.form.date-range.end-date") || "End Date";
  const rangeLabel =
    selectedRangeLabel ||
    t("common.form.date-range.selected-range") ||
    "Selected Range";
  const noneText = noneLabel || t("common.form.date-range.none") || "None";

  const headerContent = (
    <div className="p-6 border-b">
      <DialogTitle className="text-lg font-semibold">{modalTitle}</DialogTitle>
      <DialogDescription className="text-sm text-muted-foreground">
        {modalDescription}
      </DialogDescription>
    </div>
  );

  const footerContent = (
    <div className="flex items-center justify-end gap-3 p-6 border-t">
      <Button
        variant="outline"
        onClick={onClose}
        disabled={isLoading}
        className="h-10 px-6"
      >
        {cancelText}
      </Button>
      <Button
        onClick={handleSubmit}
        disabled={isLoading || !dateRange?.from || !dateRange?.to}
        className="h-10 px-6"
      >
        {isLoading ? t("common.loading") || "Loading..." : confirmText}
      </Button>
    </div>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      header={headerContent}
      className="max-w-[700px] rounded-2xl"
    >
      {/* Calendar container */}
      <div className="w-full mb-6">
        <div className="border rounded-md p-4 bg-white w-full">
          <Calendar
            locale={isAr ? ar : enUS}
            mode="range"
            selected={dateRange}
            onSelect={setDateRange}
            numberOfMonths={2}
            disabled={getDisabledDates}
            showOutsideDays={false}
            classNames={{
              root: "w-full",
              months:
                "flex gap-3 flex-col md:flex-row space-y-4 md:space-x-4 md:space-y-0 w-full justify-center",
              month: "space-y-4 w-full",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button:
                "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex w-full justify-between",
              head_cell:
                "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] text-center",
              row: "flex w-full mt-2 justify-between",
              cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
              day_selected:
                "bg-secondary text-primary-foreground hover:bg-primary rounded-sm hover:text-primary-foreground focus:bg-secondary focus:text-primary-foreground",
              day_today: "bg-gray-200 rounded-sm text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle:
                "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
          />
        </div>

        {/* Error message */}
        {error && (
          <div className="w-full mt-4">
            <p className="text-sm text-red-500 p-2 bg-red-50 rounded-md border border-red-100">
              {error}
            </p>
          </div>
        )}
      </div>

      {/* Date summary */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <h3 className="text-sm font-medium mb-3">{rangeLabel}</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-500">{startLabel}:</p>
            <p className="text-sm font-medium">
              {dateRange?.from ? formatDate(dateRange.from, locale) : noneText}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">{endLabel}:</p>
            <p className="text-sm font-medium">
              {dateRange?.to ? formatDate(dateRange.to, locale) : noneText}
            </p>
          </div>
        </div>
      </div>
      {footerContent}
    </ResponsiveDialog>
  );
}
