"use client";

import { type ReactNode, useCallback } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "./ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { Skeleton } from "./ui/skeleton";

export interface PaginationWithLinksProps {
  pageSizeSelectOptions?: {
    pageSizeSearchParam?: string;
    pageSizeOptions: number[];
  };
  totalCount: number;
  pageSize: number;
  page: number;
  firstLastCounts?: { firstCount: number; lastCount: number };
  pageSearchParam?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
}

export function PaginationWithLinks({
  pageSizeSelectOptions,
  pageSize,
  totalCount,
  page,
  firstLastCounts,
  pageSearchParam,
  isLoading = false,
  isDisabled = false,
}: PaginationWithLinksProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const totalPageCount = Math.ceil(totalCount / pageSize);

  const buildLink = useCallback(
    (newPage: number) => {
      const key = pageSearchParam || "page";
      if (!searchParams) return `${pathname}?${key}=${newPage}`;
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set(key, String(newPage));
      return `${pathname}?${newSearchParams.toString()}`;
    },
    [searchParams, pathname, pageSearchParam],
  );

  const navToPageSize = useCallback(
    (newPageSize: number) => {
      const key = pageSizeSelectOptions?.pageSizeSearchParam || "pageSize";
      const newSearchParams = new URLSearchParams(searchParams || undefined);
      newSearchParams.set(key, String(newPageSize));
      newSearchParams.delete(pageSearchParam || "page"); // Clear the page number when changing page size
      router.push(`${pathname}?${newSearchParams.toString()}`);
    },
    [
      searchParams,
      pathname,
      pageSearchParam,
      pageSizeSelectOptions?.pageSizeSearchParam,
      router,
    ],
  );

  const renderPageNumbers = () => {
    const items: ReactNode[] = [];
    const maxVisiblePages = 5;
    const isControlDisabled = isLoading || isDisabled || totalCount === 0;

    if (totalPageCount <= maxVisiblePages) {
      for (let i = 1; i <= totalPageCount; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              href={buildLink(i)}
              isActive={page === i}
              aria-disabled={isControlDisabled}
              tabIndex={isControlDisabled ? -1 : undefined}
              className={
                isControlDisabled ? "pointer-events-none opacity-50" : undefined
              }
            >
              {i}
            </PaginationLink>
          </PaginationItem>,
        );
      }
    } else {
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            href={buildLink(1)}
            isActive={page === 1}
            aria-disabled={isControlDisabled}
            tabIndex={isControlDisabled ? -1 : undefined}
            className={
              isControlDisabled ? "pointer-events-none opacity-50" : undefined
            }
          >
            1
          </PaginationLink>
        </PaginationItem>,
      );

      if (page > 3) {
        items.push(
          <PaginationItem key="ellipsis-start">
            <PaginationEllipsis />
          </PaginationItem>,
        );
      }

      const start = Math.max(2, page - 1);
      const end = Math.min(totalPageCount - 1, page + 1);

      for (let i = start; i <= end; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              href={buildLink(i)}
              isActive={page === i}
              aria-disabled={isControlDisabled}
              tabIndex={isControlDisabled ? -1 : undefined}
              className={
                isControlDisabled ? "pointer-events-none opacity-50" : undefined
              }
            >
              {i}
            </PaginationLink>
          </PaginationItem>,
        );
      }

      if (page < totalPageCount - 2) {
        items.push(
          <PaginationItem key="ellipsis-end">
            <PaginationEllipsis />
          </PaginationItem>,
        );
      }

      items.push(
        <PaginationItem key={totalPageCount}>
          <PaginationLink
            href={buildLink(totalPageCount)}
            isActive={page === totalPageCount}
            aria-disabled={isControlDisabled}
            tabIndex={isControlDisabled ? -1 : undefined}
            className={
              isControlDisabled ? "pointer-events-none opacity-50" : undefined
            }
          >
            {totalPageCount}
          </PaginationLink>
        </PaginationItem>,
      );
    }

    return items;
  };
  const t = useTranslations() as TFunction;
  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center justify-start shrink-0 gap-1 text-xs lg:text-sm max-md:hidden">
        <span className="text-[#9CA3AF]">{t("common.pagination.from")}</span>
        <span>{firstLastCounts?.firstCount}</span>
        <span className="text-[#9CA3AF]">{t("common.pagination.to")}</span>
        <span>{firstLastCounts?.lastCount}</span>
        <span className="text-[#9CA3AF]">{t("common.pagination.outOf")}</span>
        <span>
          {isLoading ? (
            <Skeleton className="w-3.5 h-3.5 rounded-full bg-gray-200" />
          ) : (
            totalCount
          )}
        </span>
        <span className="text-[#9CA3AF]">{t("common.pagination.result")}</span>
      </div>
      <Pagination
        className={cn({ "md:justify-center": pageSizeSelectOptions })}
      >
        <PaginationContent className="max-sm:gap-0 max-md:w-full">
          <PaginationItem>
            <PaginationPrevious
              href={buildLink(Math.max(page - 1, 1))}
              aria-disabled={
                page === 1 || isLoading || isDisabled || totalCount === 0
              }
              tabIndex={
                page === 1 || isLoading || isDisabled || totalCount === 0
                  ? -1
                  : undefined
              }
              className={
                page === 1 || isLoading || isDisabled || totalCount === 0
                  ? "pointer-events-none opacity-50"
                  : undefined
              }
            />
          </PaginationItem>
          <div className="flex justify-center items-center gap-1 max-sm:gap-0 max-md:hidden">
            {renderPageNumbers()}
          </div>
          {/* Mobile view*/}
          <div className="flex md:hidden items-center max-md:justify-center w-full px-4">
            <span className="text-sm font-medium flex items-center gap-1">
              <span className="text-[#9CA3AF]">
                {t("common.pagination.page")}
              </span>
              {page}
              <span className="text-[#9CA3AF]">
                {t("common.pagination.from")}
              </span>
              {isLoading ? (
                <Skeleton className="w-1 h-1 bg-gray-200" />
              ) : (
                totalPageCount
              )}
            </span>
          </div>
          <PaginationItem>
            <PaginationNext
              href={buildLink(Math.min(page + 1, totalPageCount))}
              aria-disabled={
                page === totalPageCount ||
                isLoading ||
                isDisabled ||
                totalCount === 0
              }
              tabIndex={
                page === totalPageCount ||
                isLoading ||
                isDisabled ||
                totalCount === 0
                  ? -1
                  : undefined
              }
              className={
                page === totalPageCount ||
                isLoading ||
                isDisabled ||
                totalCount === 0
                  ? "pointer-events-none opacity-50"
                  : undefined
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
      {pageSizeSelectOptions && (
        <div className="flex flex-col gap-4 flex-1">
          <SelectRowsPerPage
            options={pageSizeSelectOptions.pageSizeOptions}
            setPageSize={navToPageSize}
            pageSize={pageSize}
            isLoading={isLoading || isDisabled || totalCount === 0}
          />
        </div>
      )}
    </div>
  );
}

function SelectRowsPerPage({
  options,
  setPageSize,
  pageSize,
  isLoading,
}: {
  options: number[];
  setPageSize: (newSize: number) => void;
  pageSize: number;
  isLoading: boolean;
}) {
  const t = useTranslations() as TFunction;
  return (
    <div className="flex max-md:hidden items-center justify-center gap-2 min-w-[119px] min-h-8 rounded-lg bg-[#F3F4F6]">
      <span className="text-sm text-[#9CA3AF]">
        {t("common.pagination.show")}
      </span>

      <Select
        value={String(pageSize)}
        onValueChange={(value) => setPageSize(Number(value))}
        disabled={isLoading}
      >
        <SelectTrigger className="max-w-[57px] px-2 h-6 bg-white border-none shadow-none">
          <SelectValue placeholder="Select page size">
            {String(pageSize)}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem
              className="text-sm font-medium"
              key={option}
              value={String(option)}
            >
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
