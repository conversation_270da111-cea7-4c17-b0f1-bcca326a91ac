"use client";

import React from "react";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";
import { ApprovalStep } from "@/app/[locale]/_modules/people/type/approval-request";
import { formatDate } from "@/lib/dateFormatter";
import { ArrowConnecter } from "../../public/images/icons";

type ApprovalStepsIndicatorProps = {
  steps: ApprovalStep[];
  currentStepId?: number;
  status: "pending" | "approved" | "rejected";
  className?: string;
  showTitle?: boolean;
  lineWrapperStyle?: string;
};

const ApprovalStepsIndicator = ({
  steps,
  currentStepId,
  status,
  className,
  showTitle = true,
  lineWrapperStyle,
}: ApprovalStepsIndicatorProps) => {
  const locale = useLocale();
  const t = useTranslations();

  if (!steps || steps.length === 0) {
    return null;
  }

  // Sort steps by sequence
  const sortedSteps = [...steps].sort((a, b) => a.sequence - b.sequence);

  const getStepStatus = (step: ApprovalStep) => {
    if (step.rejected) return "rejected";
    if (step.complete) return "completed";
    if (step.id === currentStepId) return "current";
    return "pending";
  };

  const getStepIcon = (stepStatus: string, index: number) => {
    switch (stepStatus) {
      case "completed":
        return (
          <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
            <Check className="w-5 h-5 text-white stroke-[3px]" />
          </div>
        );
      case "current":
        return (
          <div className="w-10 h-10 rounded-full border-2 text-sm font-medium border-secondary flex items-center justify-center">
            <span className="text-secondary font-bold">{index + 1}</span>
          </div>
        );
      case "rejected":
        return (
          <div className="w-10 h-10 rounded-full bg-error flex items-center justify-center">
            <X className="text-white w-5 h-5 stroke-[3px]" />
          </div>
        );
      default:
        return (
          <div className="w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center">
            <span className="text-slate-500 text-sm font-medium">
              {index + 1}
            </span>
          </div>
        );
    }
  };

  const getConnectorLine = (index: number, stepStatus: string) => {
    if (index === sortedSteps.length - 1) return null;

    const isRejected = sortedSteps[index].rejected;

    if (isRejected) return null;

    const isCompleted = stepStatus === "completed";
    const lineColor = isCompleted ? "text-secondary" : "text-gray-300";

    return (
      <div
        className={cn(
          "flex-1 mt-2 w-full flex justify-end relative rtl:rotate-0 ltr:rotate-180",
          index === 1 ? "max-sm:hidden" : "",
          lineWrapperStyle,
        )}
      >
        <ArrowConnecter className={cn("h-full", lineColor)} />
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {showTitle && (
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-500 mb-2">
            {t("people.leaves-requests-page.request-details.approval-status")}
          </h3>
        </div>
      )}

      <div className="grid grid-cols-2 sm:flex items-start gap-6 sm:gap-12 ms-6">
        {sortedSteps.map((step, index) => {
          const stepStatus = getStepStatus(step);

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-start min-w-0 flex-1 max-w-[154px] sm:w-[174px]">
                {/* Step Icon */}
                <div className="w-full flex justify-between mb-4">
                  <div>{getStepIcon(stepStatus, index)}</div>
                  {/* Connector Line */}
                  {getConnectorLine(index, stepStatus)}
                </div>
                {/* Step Name */}
                <div className="text-start">
                  <div
                    className={cn(
                      "font-medium text-sm leading-5",
                      stepStatus === "completed"
                        ? "text-black"
                        : stepStatus === "current"
                          ? "text-secondary"
                          : stepStatus === "rejected"
                            ? "text-black"
                            : "text-slate-500",
                    )}
                  >
                    {step.name}
                  </div>

                  {/* Step Details */}
                  {step.actions && step.actions.length > 0 && (
                    <div className="flex items-center text-[8px] font-medium text-gray-400 mt-1">
                      {step.actions[0]?.user_id && (
                        <div>
                          {step.actions[0]?.action === "reject"
                            ? t(
                                "people.leaves-requests-page.request-details.rejection-time",
                              )
                            : t(
                                "people.leaves-requests-page.request-details.approval-time",
                              )}
                        </div>
                      )}
                      {step.actions[0]?.created_at && (
                        <div className="text-black">
                          {":  "}
                          {formatDate(step.actions[0].created_at, locale)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default ApprovalStepsIndicator;
