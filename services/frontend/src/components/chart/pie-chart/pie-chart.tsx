"use client";

import React, { JSXElementConstructor, ReactElement, SVGProps } from "react";
import { Cell, Legend, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import useMediaQuery from "@/hooks/use-media-query";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { cn } from "@/lib/utils";
import { Payload } from "recharts/types/component/DefaultTooltipContent";
import ChartHeader from "../chart-header";
import { Locale } from "@/i18n/routing";

export interface BarConfig {
  dataKey: string;
  fill: string;
  radius?: number | [number, number, number, number] | undefined;
  stackId?: string;
  // Optionally allow a custom shape; if not provided, we'll use our CustomBar.
  shape?: ReactElement<
    SVGProps<SVGPathElement>,
    string | JSXElementConstructor<any>
  >;
}

type PieChartProps<T> = {
  data?: T[];
  title: string;
  chartContainerClass?: string;
  headerClassName?: string;
} & ChartHeader;

const CustomPieChart = <T,>({
  data = [],
  headerClassName,
  title,
  selectPlaceholder,
  selectOptions,
  selectValue,
  onSelectChange,
  legendLabels,
  chartContainerClass,
}: PieChartProps<T>) => {
  const lang = useLocale();

  const textDirection = lang === LANGUAGES.ARABIC ? "rtl" : "ltr";
  const renderCustomLegend = (props: any) => {
    const { payload } = props;
    return (
      <ul
        className="flex w-full justify-evenly py-5"
        style={{
          listStyle: "none",
          display: "flex",
          direction: textDirection,
        }}
      >
        {payload.map((entry: any, index: number) => (
          <li
            key={`item-${index}`}
            className="font-alex mx-2 flex items-center text-sm"
          >
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: entry.color,
                borderRadius: "5px",
                marginLeft: 6,
              }}
            />
            <div
              className={`text-${textDirection}  text-xs font-alex`}
              dir={textDirection}
            >
              <span className="text-neutral-900">{`${entry.payload.value} `}</span>
              <span className="text-neutral-600">{entry.value}</span>
            </div>
          </li>
        ))}
      </ul>
    );
  };

  const COLORS = ["#1A4741", "#B9F78A", "#161E30", "#4E665D", "#8BA863"];
  return (
    <>
      <ChartHeader
        headerClassName={cn("p-4", headerClassName)}
        title={title}
        legendLabels={legendLabels}
        selectPlaceholder={selectPlaceholder}
        onSelectChange={onSelectChange}
        selectOptions={selectOptions}
        selectValue={selectValue}
      />
      <div className={cn(`p-4 `, chartContainerClass)}>
        <ResponsiveContainer width="100%" height={"100%"}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={true}
              dataKey="value"
              startAngle={90}
              endAngle={-270}
              stroke="none"
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                  stroke="none"
                  style={{ outline: "none" }}
                />
              ))}
            </Pie>
            <Legend content={renderCustomLegend} />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </>
  );
};

export default CustomPieChart;
