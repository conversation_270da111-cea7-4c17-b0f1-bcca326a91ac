"use client";

import React, {
  JSXElementConstructor,
  ReactElement,
  ReactNode,
  SVGProps,
} from "react";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from "recharts";
import useMediaQuery from "@/hooks/use-media-query";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { cn } from "@/lib/utils";
import { Payload } from "recharts/types/component/DefaultTooltipContent";
import ChartHeader from "../chart-header";
import { Locale } from "@/i18n/routing";

export interface BarConfig {
  dataKey: string;
  fill: string;
  radius?: number | [number, number, number, number] | undefined;
  stackId?: string;
  // Optionally allow a custom shape; if not provided, we'll use our CustomBar.
  shape?: ReactElement<
    SVGProps<SVGPathElement>,
    string | JSXElementConstructor<any>
  >;
}

type GenericBarChartProps<T> = {
  data?: T[];
  xAxisKey: keyof T;
  // Optional formatter for the X-axis tick values
  xAxisTickFormatter?: (value: any) => string;
  // Custom tick renderer or style object for the X-axis
  xAxisTick?: ReactElement | ((props: any) => ReactNode) | object;
  // Custom tick renderer or style object for the Y-axis
  yAxisTick?: ReactElement | ((props: any) => ReactNode) | object;
  xAxisAngle?: number;
  yAxisDomain?: [number, number | string];
  // Configuration for each bar you want to render
  bars: BarConfig[];
  barSize?: number;
  // Optional Y-axis ticks array
  ticks?: number[];
  // Optional text direction (ltr/rtl)
  textDirection?: "ltr" | "rtl";
  // Optional margin object for the chart
  margin?: {
    top?: number;
    right?: number;
    left?: number;
    bottom?: number;
  };
  // Optional tooltip label formatter
  tooltipLabelFormatter?: (
    label: any,
    payload: Payload<any, any>[],
  ) => ReactNode;

  // Optional tooltip formatter
  tooltipFormatter?: (
    value: any,
    name: any,
    payload: any,
  ) => ReactNode | [ReactNode, any];
  stackOffset:
    | "none"
    | "wiggle"
    | "silhouette"
    | "expand"
    | "positive"
    | "sign";
  gridStrokeDasharray?: string;
  chartContainerClass?: string;
} & ChartHeader;

const GenericBarChart = <T,>({
  data = [],
  stackOffset,
  xAxisKey,
  xAxisTickFormatter,
  xAxisTick,
  yAxisTick,
  xAxisAngle,
  yAxisDomain,
  bars,
  ticks = [0, 2, 4, 6, 8, 10],
  margin,
  tooltipLabelFormatter,
  barSize,
  tooltipFormatter,
  gridStrokeDasharray,
  headerClassName,
  title,
  selectPlaceholder,
  selectOptions,
  selectValue,
  onSelectChange,
  legendLabels,
  chartContainerClass,
}: GenericBarChartProps<T>) => {
  const isMobile = useMediaQuery("(max-width:580px)");
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  // Provide default tooltip formatters if none are supplied
  const defaultTooltipLabelFormatter = (label: any) => `${label}`;
  const defaultTooltipFormatter = (value: any, name: any) => {
    return [
      <div className="flex justify-between w-full items-center" key={name}>
        <span className="text-[10px] font-semibold">{name}</span>
        <span className="text-xs text-black font-semibold">{value}</span>
      </div>,
    ];
  };

  return (
    <>
      <ChartHeader
        headerClassName={cn("p-4", headerClassName)}
        title={title}
        legendLabels={legendLabels}
        selectPlaceholder={selectPlaceholder}
        onSelectChange={onSelectChange}
        selectOptions={selectOptions}
        selectValue={selectValue}
      />
      <div className={cn("p-4 pt-0", chartContainerClass)}>
        <ResponsiveContainer width="100%" height={"100%"}>
          <BarChart
            data={data}
            stackOffset={stackOffset}
            margin={margin}
            barSize={barSize ?? 35.45}
            barCategoryGap={10}
          >
            <XAxis
              reversed={isAr}
              dataKey={xAxisKey as string}
              // Use custom tick if provided; otherwise, fall back to default inline styles.
              tick={
                xAxisTick || {
                  fill: "black",
                  fontSize: isMobile ? 8 : 10,
                  fontWeight: 400,
                }
              }
              angle={isMobile ? -45 : 0}
              tickFormatter={xAxisTickFormatter}
              interval={0}
              tickMargin={15}
              tickSize={0}
              axisLine={false}
              tickLine={{ stroke: "#E5E6E6" }}
            />
            <YAxis
              orientation={isAr ? "right" : "left"}
              type="number"
              domain={yAxisDomain || [0, "dataMax"]}
              ticks={ticks}
              tickMargin={20}
              tickSize={0}
              axisLine={false}
              tickLine={false}
              // Use custom tick if provided; otherwise, fall back to default inline styles.
              tick={
                yAxisTick || {
                  fill: "black",
                  fontSize: isMobile ? 8 : 10,
                  fontWeight: 400,
                }
              }
              includeHidden
            />
            <CartesianGrid
              syncWithTicks={true}
              vertical={false}
              stroke="#E5E6E6"
              strokeDasharray={gridStrokeDasharray}
            />
            <Tooltip
              cursor={false}
              wrapperStyle={{
                borderRadius: "8px",
                boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.15)",
                minWidth: "120px",
              }}
              contentStyle={{
                borderRadius: "8px",
                padding: "8px 10px",
                backgroundColor: "#FBFBFC",
                border: "#E5E6E6",
              }}
              labelStyle={{
                color: "#6B7271",
                fontSize: "12px",
                fontWeight: 600,
              }}
              itemStyle={{
                color: "#6B7271",
                fontSize: "10px",
                height: "16px",
                fontWeight: 600,
              }}
              labelFormatter={
                tooltipLabelFormatter || defaultTooltipLabelFormatter
              }
              formatter={tooltipFormatter || defaultTooltipFormatter}
            />
            {bars.map((bar, index) => (
              <Bar
                key={index}
                dataKey={bar.dataKey}
                stackId={bar.stackId}
                fill={bar.fill}
                radius={bar.radius}
                shape={bar.shape}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>
    </>
  );
};

export default GenericBarChart;
