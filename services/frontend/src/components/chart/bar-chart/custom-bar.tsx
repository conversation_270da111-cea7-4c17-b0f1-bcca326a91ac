"use client";

import { Rectangle } from "recharts";

export const CustomBar = (props: any) => {
  const { x, y, width, height, baseline = 295 } = props;
  const offset = 10;
  let newY = y - offset;
  if (newY + height > baseline) {
    newY = baseline - height;
  }
  return <Rectangle {...props} y={newY} width={width} height={height} />;
};

export function HighlightCurrentMonthShape(props: any) {
  const { x, y, width, height, payload } = props;
  const isHighestPercentage = payload.highlight;
  const fillColor = isHighestPercentage ? "hsl(var(--primary))" : "#E5E7EB";

  return (
    <Rectangle
      {...props}
      fill={fillColor}
      x={x}
      y={y}
      width={width}
      height={height}
    />
  );
}
