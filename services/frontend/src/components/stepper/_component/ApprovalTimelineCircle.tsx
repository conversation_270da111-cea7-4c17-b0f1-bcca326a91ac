import React from "react";
import classNames from "classnames";
import { ApprovalStatus } from "@/enums/procure";

interface ApprovalTimelineCircleProps {
  status: ApprovalStatus;
}

const ApprovalTimelineCircle: React.FC<ApprovalTimelineCircleProps> = ({
  status,
}) => (
  <div
    className={classNames(
      "w-[20px] h-[20px] rounded-full border-2 flex items-center justify-center text-white text-xs font-bold z-10 shrink-0",
      {
        "bg-success-600 border-success-600 text-success-700":
          status === ApprovalStatus.Accepted,
        "bg-red-500 border-red-500 text-white":
          status === ApprovalStatus.Rejected,
        "bg-gray-400 border-gray-400 text-white":
          status === ApprovalStatus.Pending,
      },
    )}
  >
    {status === ApprovalStatus.Accepted ? (
      <svg
        className="w-[10px] h-[10px]"
        fill="none"
        stroke="currentColor"
        strokeWidth={2.5}
        viewBox="0 0 24 24"
      >
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
      </svg>
    ) : status === ApprovalStatus.Rejected ? (
      <svg
        className="w-[10px] h-[10px]"
        fill="none"
        stroke="currentColor"
        strokeWidth={2.5}
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    ) : (
      "..."
    )}
  </div>
);

export default ApprovalTimelineCircle;
