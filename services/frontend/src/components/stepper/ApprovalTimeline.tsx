"use client";

import React from "react";
import classNames from "classnames";
import { ApprovalDirection, ApprovalStatus } from "@/enums/procure";
import ApprovalTimelineCircle from "./_component/ApprovalTimelineCircle";
import { ApprovalItem } from "@/types/procure";

type ApprovalTimelineProps = {
  items: ApprovalItem[];
  direction?: ApprovalDirection;
  title?: string;
  subtitle?: string;
};

// Utilities
const getStatusColor = (status: ApprovalStatus) => {
  switch (status) {
    case ApprovalStatus.Accepted:
      return "green";
    case ApprovalStatus.Rejected:
      return "red";
    default:
      return "gray";
  }
};

const getStatusStyle = (color: string) => {
  switch (color) {
    case "green":
      return "bg-success-50 text-success-900";
    case "red":
      return "bg-red-100 text-red-700";
    default:
      return "bg-gray-200 text-gray-600";
  }
};

// Timeline Card
const ApprovalTimelineCard: React.FC<{
  item: ApprovalItem;
  isVertical: boolean;
  isLast: boolean;
  nextStatus?: ApprovalStatus;
}> = ({ item, isVertical, isLast }) => {
  const statusColor = getStatusColor(item.status);
  const statusStyle = getStatusStyle(statusColor);
  const connectorColor = "bg-neutral-200";

  return (
    <div
      className={classNames(
        "relative",
        isVertical
          ? "flex items-start mb-6"
          : "flex flex-col items-center text-center flex-1",
      )}
    >
      {/* Connector Line */}
      {!isLast && (
        <div
          className={classNames(
            "absolute z-0",
            isVertical
              ? "right-[11px] top-7 w-px h-8"
              : "top-1/2 right-full transform -translate-y-1/2 h-px w-10",
            connectorColor,
          )}
        />
      )}

      <ApprovalTimelineCircle status={item.status} />

      <div className={classNames(isVertical ? "pr-4" : "mt-2 max-w-[140px]")}>
        <div
          className={classNames(
            "flex items-center gap-2",
            isVertical ? "mb-1" : "flex-col text-xs",
          )}
        >
          <span className="font-medium text-sm sm:text-base text-neutral-500">
            {item.title},
          </span>
          <span className="text-xs md:text-base font-medium">
            {item.subtitle}
          </span>
          <span
            className={classNames(
              "text-xs sm:text-sm font-medium px-4 py-1 rounded-sm",
              statusStyle,
            )}
          >
            {item.status}
          </span>
        </div>
        <div className="text-xs text-gray-500">{item.date}</div>
      </div>
    </div>
  );
};

// Timeline Wrapper
const ApprovalTimeline: React.FC<ApprovalTimelineProps> = ({
  items,
  direction = ApprovalDirection.Vertical,
}) => {
  const isVertical = direction === ApprovalDirection.Vertical;

  return (
    <div dir="rtl" className="bg-white">
      <div
        className={classNames("relative", {
          "pl-2": isVertical,
          "flex gap-6 justify-between": !isVertical,
        })}
      >
        {items.map((item, index) => (
          <ApprovalTimelineCard
            key={item.id}
            item={item}
            isVertical={isVertical}
            isLast={index === items.length - 1}
            nextStatus={items[index + 1]?.status}
          />
        ))}
      </div>
    </div>
  );
};

export default ApprovalTimeline;
