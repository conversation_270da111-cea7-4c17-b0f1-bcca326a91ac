div.doc-container {
  width: 100%;
  height: 100%;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  div#proxy-renderer {
    height: 100%;
    & #msdoc-renderer {
      height: 100%;
    }
  }

  div#pdf-controls {
    position: absolute;
    top: 0;
    right: 0;
    justify-content: flex-start;
    gap: 10px;
    padding: 8px;
  }
}

/* Hide text layer in PDF viewer */
.react-pdf__Page__textContent.textLayer {
  visibility: hidden;
  opacity: 0;
}
