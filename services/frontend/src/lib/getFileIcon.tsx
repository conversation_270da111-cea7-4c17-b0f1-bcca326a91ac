import React from "react";
import Image from "next/image";
import { File } from "lucide-react";
import { PDFICON } from "../../public/images/icons";

type FileIconProps = {
  fileName: string;
  fileType?: string;
  fileUrl?: string;
  width?: number;
  height?: number;
  className?: string;
};

export const getFileIcon = ({
  fileName,
  fileType,
  fileUrl,
  width = 80,
  height = 80,
  className = "w-20 h-20",
}: FileIconProps): React.ReactNode => {
  const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";
  const imageTypes = ["jpg", "jpeg", "png", "gif", "webp", "svg"];

  // PDF files
  if (fileType === "application/pdf" || fileExtension === "pdf") {
    return (
      <Image
        className={`${className} object-contain rounded-sm`}
        alt={`${fileName} `}
        src={"/images/icons/pdf-icon.svg"}
        width={width}
        height={height}
      />
    );
  }
  // Image files
  else if (imageTypes.includes(fileExtension) && fileUrl) {
    return (
      <Image
        className={`${className} object-contain rounded-sm`}
        alt={`${fileName} preview`}
        src={fileUrl}
        width={width}
        height={height}
      />
    );
  }
  // Default file icon
  else {
    return <File className={className || "text-gray-100"} />;
  }
};

/**
 * Component version of getFileIcon for easier use in JSX
 */
export const FileIcon: React.FC<FileIconProps> = (props) => {
  return <>{getFileIcon(props)}</>;
};

export default FileIcon;
