import { ColumnDef } from "@tanstack/react-table";
import { format, parseISO, isValid } from 'date-fns';

interface FilterableColumn {
  id: string;
  filterType: "text" | "date" | "number" | "boolean" | "custom";
  customParser?: (value: string) => any;
  customFormatter?: (value: any) => string;
}

interface ModuleFilterConfig {
  [tablePrefix: string]: FilterableColumn[];
}

type TableColumnDef = ColumnDef<any> & {
  meta?: {
    filterType?: "text" | "date" | "number" | "boolean" | "custom";
    customParser?: (value: string) => any;
    customFormatter?: (value: any) => string;
  };
};

class FilterTypeManager {
  private typeCache = new Map<string, FilterableColumn>();
  private static instance: FilterTypeManager;
  private registeredTables = new Map<string, TableColumnDef[]>();

  static getInstance(): FilterTypeManager {
    if (!FilterTypeManager.instance) {
      FilterTypeManager.instance = new FilterTypeManager();
    }
    return FilterTypeManager.instance;
  }

  registerTable(tableId: string, columns: TableColumnDef[]): void {
    this.registeredTables.set(tableId, columns);
    this.clearCache();
  }

  getFieldConfig(
    tableId: string,
    fieldId: string,
  ): FilterableColumn | null {
    const cacheKey = `${tableId}:${fieldId}`;

    if (this.typeCache.has(cacheKey)) {
      return this.typeCache.get(cacheKey)!;
    }

    const columns = this.registeredTables.get(tableId);

    if (columns) {
      const column = columns.find((col) => {
        const accessorKey = typeof (col as any).accessorKey === 'string' ? (col as any).accessorKey : '';
        const id = col.id || '';
        return accessorKey === fieldId || id === fieldId;
      });

      if (column?.meta?.filterType) {
        const fieldConfig: FilterableColumn = {
          id: fieldId,
          filterType: column.meta.filterType,
          customParser: column.meta.customParser,
          customFormatter: column.meta.customFormatter,
        };

        this.typeCache.set(cacheKey, fieldConfig);
        return fieldConfig;
      }
    }

    return null;
  }

  parseValue(rawValue: any, tableId: string, fieldId: string): any {
    if (rawValue == null) return rawValue;

    const config = this.getFieldConfig(tableId, fieldId);
    if (!config) return String(rawValue);

    try {
      switch (config.filterType) {
        case 'date': return this.parseDate(rawValue);
        case 'number': return this.parseNumber(rawValue);
        case 'boolean': return this.parseBoolean(rawValue);
        case 'custom': return this.parseCustom(rawValue, config);
        default: return String(rawValue);
      }
    } catch (error) {
      console.error(`Error parsing filter value for ${tableId}.${fieldId}:`, error);
      return rawValue;
    }
  }

  formatValue(typedValue: any, tableId: string, fieldId: string): string {
    if (typedValue == null) return '';

    const config = this.getFieldConfig(tableId, fieldId);

    if (!config) {
      return String(typedValue);
    }

    try {
      switch (config.filterType) {
        case 'date':
          const dateResult = this.formatDate(typedValue);
          return dateResult;
        case 'number': return this.formatNumber(typedValue);
        case 'boolean': return this.formatBoolean(typedValue);
        case 'custom': return this.formatCustom(typedValue, config);
        default: return String(typedValue);
      }
    } catch (error) {
      console.error(`Error formatting filter value for ${tableId}.${fieldId}:`, error);
      return String(typedValue);
    }
  }

  // Private type-specific methods - SINGLE implementation each
  private parseDate(value: any): Date {
    if (value instanceof Date) return value;
    const parsed = parseISO(String(value));
    return isValid(parsed) ? parsed : new Date(String(value));
  }

  private formatDate(value: any): string {
    // Convert to Date if not already a Date
    const dateValue = value instanceof Date ? value : this.parseDate(value);
    return format(dateValue, 'yyyy-MM-dd'); // Timezone-safe
  }

  private parseBoolean(value: any): boolean {
    if (typeof value === 'boolean') return value;
    return String(value).toLowerCase() === 'true';
  }

  private formatBoolean(value: any): string {
    return String(value);
  }

  private parseNumber(value: any): number {
    const num = Number(value);
    return isNaN(num) ? value : num;
  }

  private formatNumber(value: any): string {
    return String(value);
  }

  private parseCustom(value: any, config: FilterableColumn): any {
    return config.customParser ? config.customParser(String(value)) : value;
  }

  private formatCustom(value: any, config: FilterableColumn): string {
    return config.customFormatter ? config.customFormatter(value) : String(value);
  }

  clearCache(): void {
    this.typeCache.clear();
  }

  getTableFields(tableId: string): FilterableColumn[] {
    const columns = this.registeredTables.get(tableId);
    if (!columns) return [];

    return columns
      .filter(col => col.meta?.filterType)
      .map(col => ({
        id: col.id || (typeof (col as any).accessorKey === 'string' ? (col as any).accessorKey : ''),
        filterType: col.meta!.filterType!,
        customParser: col.meta!.customParser,
        customFormatter: col.meta!.customFormatter,
      }));
  }
}

export const filterTypeManager = FilterTypeManager.getInstance();

export type { FilterableColumn, ModuleFilterConfig };
