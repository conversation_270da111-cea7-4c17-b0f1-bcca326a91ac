"use client";

import { TSystems } from "@/types";

export function extractSystemFromRoute(route: string): TSystems | null {
  const segments = route.split("/").filter(Boolean);
  if (segments.length >= 2) {
    const systemSegment = segments[1];
    // Check if it's a valid system
    const validSystems: TSystems[] = ["people", "procure", "cm"];
    if (validSystems.includes(systemSegment as TSystems)) {
      return systemSegment as TSystems;
    }
  }

  return null;
}
export function getRedirectPathAfterSystemSelection(
  selectedSystem: string,
  lastRoute: string | null,
  locale: string,
): string {
  const defaultPath = `/${locale}/${selectedSystem}`;

  if (!lastRoute) {
    return defaultPath;
  }

  const lastRouteSystem = extractSystemFromRoute(lastRoute);

  if (lastRouteSystem === selectedSystem.replace("/", "")) {
    return lastRoute;
  }

  // If user selects a different system, go to default path
  return defaultPath;
}
