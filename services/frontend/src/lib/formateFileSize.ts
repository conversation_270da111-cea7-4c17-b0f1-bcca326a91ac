import { TFunction } from "@/types";

export function formatFileSize(bytes: number, t: TFunction): string {
  if (bytes < 1024) {
    return `${bytes} ${t("common.fileSize.bytes")}`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} ${t("common.fileSize.kilobytes")}`;
  } else if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} ${t(
      "common.fileSize.megabytes",
    )}`;
  } else {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} ${t(
      "common.fileSize.gigabytes",
    )}`;
  }
}
