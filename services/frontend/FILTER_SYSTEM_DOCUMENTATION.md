# Filter System Refactor Documentation

## Overview

This document explains the refactored filter system that replaces the old hardcoded filter implementation. The new system eliminates manual column mapping, fixes date formatting issues, and provides dynamic table registration with proper Ransack operator support.

## Key Problems Solved

### 🎯 **Main Issues Fixed**
- **Date filters broken**: Original system sent raw JavaScript date strings (`toISOString()`) causing server-side parsing issues
- **Hardcoded column mapping**: All filterable columns were manually defined in `filter-utils.ts`
- **Inconsistent URL structure**: Used nested `filters` parameter instead of direct query parameters
- **Limited operator support**: Only basic operators like `contains`, `equals`, `before`, `after`
- **No cards view filtering**: Filters only worked in table view, not cards view

### 🚀 **New Features**
- **Dynamic column registration**: Tables register their own filterable columns
- **Proper date formatting**: Dates formatted as `yyyy-MM-dd` for server compatibility
- **Ransack operator support**: Full support for `eq`, `cont`, `gt`, `lt`, `gteq`, `lteq`, etc.
- **Boolean filter support**: Dropdown selection for true/false values
- **Universal filtering**: Works consistently across table and cards views
- **Better debugging**: Comprehensive logging for troubleshooting

## Architecture Changes

### Old System (develop branch)

#### **Manual Column Mapping**
```typescript
// OLD: Hardcoded in filter-utils.ts
const tableFilterColumns: Record<string, FilterableColumn[]> = {
  "people.employees-page.table": [
    { id: "employeeName", filterType: "text" },
    { id: "registrationDate", filterType: "date" },
  ],
  "people.devices-page.table": [
    { id: "device-name", filterType: "text" },
    { id: "created_at", filterType: "date" },
  ],
};
```

#### **Problematic URL Structure**
```
// OLD: Nested filters parameter
?filters=filter%5Bcreated_at_gt%5D=2025-01-01T00%3A00%3A00.000Z

// OLD: Date sent as ISO string causing parsing issues
filter[created_at_gt]=2025-01-01T00:00:00.000Z
```

### New System

#### **Dynamic Registration**
```typescript
// NEW: Tables register themselves
const DevicesTable = () => {
  useTableRegistration("devices", columns); // Dynamic registration
  // ...
};
```

#### **Clean URL Structure**
```
// NEW: Direct query parameters
?filter[created_at_eq]=2025-01-01&filter[name_cont]=device

// NEW: Properly formatted dates
filter[created_at_eq]=2025-01-01
```

### Core Components

#### 1. **FilterTypeManager** (`src/lib/filter-type-manager.ts`)
- **Purpose**: Central registry replacing hardcoded column mappings
- **Key Features**:
  - Dynamic table registration
  - Type-aware value parsing and formatting
  - Caching for performance
  - Support for date, number, boolean, and text types

#### 2. **useTableRegistration Hook** (`src/hooks/useTableRegistration.ts`)
- **Purpose**: Replaces manual column definitions
- **Usage**: `useTableRegistration("tableName", columns)`
- **Required**: Must be called in every filterable component

#### 3. **New Filter Hooks**
- **useFilterParams**: Parses URL parameters with proper type conversion
- **useFilterNavigation**: Manages filter URL updates
- **useApiUrl**: Builds API URLs with formatted filter parameters

## Migration Guide

### Before vs After Comparison

#### **Old System (develop branch)**
```typescript
// ❌ Manual column mapping in filter-utils.ts
const tableFilterColumns: Record<string, FilterableColumn[]> = {
  "people.devices-page.table": [
    { id: "device-name", filterType: "text" },
    { id: "created_at", filterType: "date" },
  ],
};

// ❌ Problematic date handling in filter-url-parser.ts
const valueToSend = rule.value instanceof Date
  ? rule.value.toISOString()  // Sends: 2025-01-01T00:00:00.000Z
  : rule.value.toString();

// ❌ Nested URL structure
?filters=filter%5Bcreated_at_gt%5D=2025-01-01T00%3A00%3A00.000Z

// ❌ Basic operators only
case "contains": return "cont";
case "equals": return "eq";
case "before": return "lt";
case "after": return "gt";
```

#### **New System**
```typescript
// ✅ Dynamic registration
useTableRegistration("devices", columns);

// ✅ Proper date formatting
formatDate(value): string {
  return format(dateValue, 'yyyy-MM-dd'); // Sends: 2025-01-01
}

// ✅ Clean URL structure
?filter[created_at_eq]=2025-01-01&filter[name_cont]=device

// ✅ Full Ransack operator support
eq, not_eq, cont, not_cont, gt, gteq, lt, lteq, null, not_null, etc.
```

### Migration Steps

#### 1. **Remove Manual Column Definitions**
```typescript
// DELETE: Remove from filter-utils.ts (this is now automated)
const tableFilterColumns: Record<string, FilterableColumn[]> = {
  "people.devices-page.table": [...], // ❌ Delete this
};
```

#### 2. **Add Dynamic Registration**
```typescript
// ADD: In your table component
import { useTableRegistration } from "@/hooks/useTableRegistration";

const DevicesTable = () => {
  useTableRegistration("devices", columns); // ✅ Add this
  // ... rest of component
};
```

#### 3. **Update Column Definitions**
```typescript
// MODIFY: Add meta.filterType to columns
export const columns: ColumnDef<TDevice>[] = [
  {
    accessorKey: "name",
    header: "Device Name",
    enableColumnFilter: true,    // ✅ Add this
    meta: {
      filterType: "text"         // ✅ Add this
    }
  },
  {
    accessorKey: "created_at",
    header: "Created Date",
    enableColumnFilter: true,    // ✅ Add this
    meta: {
      filterType: "date"         // ✅ Add this
    }
  },
];
```

#### 4. **Update Cards Components**
```typescript
// ADD: Registration to cards view (if exists)
const DevicesCards = () => {
  useTableRegistration("devices", columns); // ✅ Same registration
  // ... rest of component
};
```

## Supported Filter Types

### 1. **Date Filters**
- **Old Problem**: Sent as `2025-01-01T00:00:00.000Z` (ISO string)
- **New Solution**: Formatted as `2025-01-01` (server-compatible)
- **Input**: Date picker component
- **Example**: `filter[created_at_eq]=2025-06-03`

### 2. **Boolean Filters** (New Feature)
- **Input**: Select dropdown (True/False)
- **Output**: `"true"` or `"false"` strings
- **Example**: `filter[is_active_eq]=true`

### 3. **Number Filters**
- **Input**: Number input
- **Output**: Numeric strings
- **Example**: `filter[port_gt]=8080`

### 4. **Text Filters**
- **Input**: Text input
- **Output**: String values
- **Example**: `filter[name_cont]=device`

## Real Implementation Examples

### Devices Table Migration
```typescript
// OLD: Manual definition in filter-utils.ts
"people.devices-page.table": [
  { id: "device-name", filterType: "text" },
  { id: "created_at", filterType: "date" },
],

// NEW: Dynamic column definitions
export const columns: ColumnDef<TDevice>[] = [
  {
    accessorKey: "name",           // ✅ Changed from "device-name"
    enableColumnFilter: true,      // ✅ Added
    meta: { filterType: "text" },  // ✅ Added
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.name")}</div>;
    },
  },
  {
    accessorKey: "created_at",
    enableColumnFilter: true,      // ✅ Added
    meta: { filterType: "date" },  // ✅ Added
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.created_at")}</div>;
    },
  },
];

// NEW: Registration in both table and cards components
const DevicesTable = () => {
  useTableRegistration("devices", columns); // ✅ Added
  // ... rest of component
};

const DevicesCards = () => {
  useTableRegistration("devices", columns); // ✅ Added
  // ... rest of component
};
```

## Files Changed

### Removed Files (from develop)
- `src/components/table/filter/filter-url-parser.ts` → Replaced by `ransack-utils.ts`
- `src/components/table/filter/filter-utils-mapping.ts` → Replaced by `FilterTypeManager`

### New Files Added
- `src/lib/filter-type-manager.ts` → Central type management
- `src/hooks/useTableRegistration.ts` → Dynamic table registration
- `src/hooks/filters/useFilterParams.ts` → URL parameter parsing
- `src/hooks/filters/useFilterNavigation.ts` → Filter navigation
- `src/components/table/filter/ransack-utils.ts` → Ransack utilities
- `src/hooks/useApiUrl.ts` → API URL building

### Modified Files
- **Table components**: Added `useTableRegistration` calls
- **Column definitions**: Added `enableColumnFilter` and `meta.filterType`
- **Filter components**: Updated to support new architecture
- **Translation files**: Added new Ransack operator translations

## Breaking Changes

### API Changes
- **FilterModal**: Now requires `tablePrefix` prop
- **TableHeaderControls**: Added optional `tablePrefix` prop
- **Column definitions**: Must include `meta.filterType` for filterable columns

### URL Structure Change
```typescript
// OLD: Nested filters parameter
?filters=filter%5Bcreated_at_gt%5D=2025-01-01T00%3A00%3A00.000Z

// NEW: Direct query parameters
?filter[created_at_eq]=2025-01-01&filter[name_cont]=device
```

### Operator Changes
```typescript
// OLD: Basic operators
"contains", "equals", "before", "after", "greater", "less"

// NEW: Ransack operators
"eq", "not_eq", "cont", "not_cont", "gt", "gteq", "lt", "lteq", "null", "not_null"
```

## Debugging

The new system includes comprehensive debug logging:

```javascript
// FilterTypeManager registration
🔥 REGISTERING TABLE: devices with 11 columns
🔥 getFieldConfig: looking for devices.created_at

// Type conversion logs
🚨 formatFilterValue ENTRY: {value: "2025-01-01", tablePrefix: "devices", fieldId: "created_at"}
🔍 formatFilterValues called with: {filters: {...}, tablePrefix: "devices"}
```

## Troubleshooting

### Common Issues

#### **Filters not working**
- Ensure `useTableRegistration("tableName", columns)` is called
- Verify columns have `enableColumnFilter: true` and `meta.filterType`
- Check browser console for FilterTypeManager debug logs

#### **Date filters not formatting correctly**
- Verify table registration uses simple name (e.g., `"devices"` not `"people.devices-page.table"`)
- Check that date columns have `meta: { filterType: "date" }`
- Look for FilterTypeManager logs showing field config

#### **Cards view filters not working**
- Add `useTableRegistration` to cards component
- Ensure both table and cards use the same table name
- Verify both components import the same column definitions

### Debug Steps
1. Check browser console for registration logs
2. Verify URL parameters are formatted correctly
3. Confirm FilterTypeManager finds field configurations
4. Test that API receives properly formatted filter values
