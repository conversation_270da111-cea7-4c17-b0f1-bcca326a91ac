name: athar-frontend

services:
  app:
    hostname: frontend-app
    build:
      context: ./
      dockerfile: ./development.dockerfile
    env_file:
      - docker-compose.env
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`frontend.athar.test`)"
      - "traefik.http.routers.frontend.entrypoints=web,websecure"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
      - "traefik.http.routers.frontend.tls=false"

      # Health Check Configuration
      - "traefik.http.services.frontend.loadbalancer.healthcheck.path=/api/health"
      - "traefik.http.services.frontend.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.frontend.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    volumes:
      - ./:/app

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
